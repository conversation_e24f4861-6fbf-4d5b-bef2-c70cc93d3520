-- Fix user registration issues
-- This script fixes the handle_new_user trigger function that creates user profiles

-- First, check if the email_logs table exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'email_logs'
  ) THEN
    CREATE TABLE email_logs (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      order_id UUID REFERENCES orders(id) ON DELETE CASCADE NULL,
      email_type TEXT NOT NULL,
      recipient TEXT NOT NULL,
      status TEXT NOT NULL,
      sent_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create index for faster lookups
    CREATE INDEX idx_email_logs_order_id ON email_logs(order_id);
    CREATE INDEX idx_email_logs_email_type ON email_logs(email_type);
    
    -- Set up Row Level Security (RLS)
    ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;
    
    -- Create policies for email_logs
    CREATE POLICY "Ad<PERSON> can view all email logs"
      ON email_logs FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
    
    CREATE POLICY "Users can view their own email logs"
      ON email_logs FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM orders
          WHERE orders.id = email_logs.order_id
          AND orders.user_id = auth.uid()
        )
      );
    
    -- Only the system can insert email logs
    CREATE POLICY "System can insert email logs"
      ON email_logs FOR INSERT
      WITH CHECK (auth.uid() IS NOT NULL);
      
    RAISE NOTICE 'Created email_logs table';
  END IF;
END $$;

-- Fix the handle_new_user function to be more robust
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  -- Check if a profile already exists for this user
  -- This prevents duplicate key errors
  IF EXISTS (
    SELECT 1 FROM public.user_profiles WHERE id = new.id
  ) THEN
    -- Profile already exists, just update it
    UPDATE public.user_profiles
    SET 
      display_name = COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      email = new.email,
      updated_at = now()
    WHERE id = new.id;
  ELSE
    -- Insert new profile
    INSERT INTO public.user_profiles (
      id, 
      display_name, 
      email, 
      role,
      created_at,
      updated_at
    )
    VALUES (
      new.id, 
      COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      new.email,
      'user',  -- Default role is always 'user'
      now(),
      now()
    );
  END IF;
  
  -- Don't try to insert into email_logs if it doesn't exist yet
  -- This prevents errors during initial setup
  IF EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'email_logs'
  ) THEN
    -- Log the welcome email in email_logs to track it
    INSERT INTO email_logs (
      order_id,
      email_type,
      recipient,
      status,
      sent_at
    ) VALUES (
      NULL, -- No order associated with welcome email
      'welcome_email',
      new.email,
      'pending', -- Mark as pending until it's actually sent
      NULL -- Will be updated when email is sent
    );
  END IF;
  
  RETURN new;
EXCEPTION
  WHEN others THEN
    -- Log the error but don't prevent user creation
    RAISE WARNING 'Error in handle_new_user trigger: %', SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;
