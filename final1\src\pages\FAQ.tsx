import React from 'react';
import ResponsiveLayout from '@/components/layout/ResponsiveLayout';
import PageContainer from '@/components/layout/PageContainer';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const FAQ = () => {
  const faqItems = [
    {
      question: "How do I place an order?",
      answer: "You can place an order by browsing our products, selecting the items you want, and adding them to your cart. Once you're ready to checkout, follow the prompts to enter your shipping and payment information. You'll receive an order confirmation email once your purchase is complete."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards (Visa, MasterCard, American Express), debit cards, UPI, net banking, and wallet payments. All transactions are secure and encrypted for your protection."
    },
    {
      question: "How long will it take to receive my order?",
      answer: "Standard delivery typically takes 5-7 business days within metro cities and 7-14 business days for other locations. For custom furniture pieces, please allow 1-3 weeks for production and delivery. You'll receive tracking information once your order ships."
    },
    {
      question: "Can I modify or cancel my order?",
      answer: "Orders can be modified or canceled within 24 hours of placement. For changes after this period, please contact our customer service team as soon as possible, and we'll do our best to accommodate your request. Custom orders cannot be canceled once production has begun."
    },
    {
      question: "Do you offer assembly services?",
      answer: "Yes, we offer professional assembly services for all our furniture. The assembly fee varies based on the product and your location. You can select this option during checkout. Our team will contact you to schedule a convenient time for assembly."
    },
    
    {
      question: "How do I care for my furniture?",
      answer: "Each piece of furniture requires specific care depending on the materials used. General recommendations include keeping wooden furniture away from direct sunlight, using coasters for hot items, and regular dusting. Please refer to our Care Instructions page for detailed guidance on maintaining your specific furniture pieces."
    },
    {
      question: "Do you ship internationally?",
      answer: "Currently, we only ship within India. We're working on expanding our shipping options to international locations in the future."
    },
    
    {
      question: "Can I request custom dimensions for furniture?",
      answer: "Yes, we specialize in custom furniture solutions. Please visit our Custom Interiors page or contact our design team to discuss your specific requirements and get a personalized quote."
    }
  ];

  return (
    <ResponsiveLayout>
      <div className="py-12 md:py-16 lg:py-20 bg-badhees-50">
        <PageContainer>
          <div className="max-w-3xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-6">Frequently Asked Questions</h1>
            <p className="text-badhees-600 mb-8">
              Find answers to common questions about our products, services, shipping, returns, and more. If you can't find what you're looking for, please don't hesitate to contact our customer service team.
            </p>

            <Accordion type="single" collapsible className="bg-white rounded-lg shadow-sm border border-badhees-100">
              {faqItems.map((item, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="px-6 py-4 hover:bg-badhees-50 text-badhees-800 font-medium">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-4 pt-1 text-badhees-600">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>

            <div className="mt-12 bg-white p-6 rounded-lg shadow-sm border border-badhees-100">
              <h2 className="text-xl font-semibold text-badhees-800 mb-4">Still Have Questions?</h2>
              <p className="text-badhees-600 mb-4">
                Our customer service team is here to help. Contact us through any of the following methods:
              </p>
              <ul className="space-y-2 text-badhees-600">
                <li>Email: <EMAIL></li>
                <li>Phone: 9108344363, 8197705438</li>
                <li>Hours: Monday to Friday, 9:00 AM to 6:00 PM IST</li>
              </ul>
            </div>
          </div>
        </PageContainer>
      </div>
    </ResponsiveLayout>
  );
};

export default FAQ;
