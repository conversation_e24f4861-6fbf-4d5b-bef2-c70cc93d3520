# Fix User Profile System

This guide explains how to fix the user profile system in the Supabase backend.

## The Problem

Users can register and log in successfully, but their profiles are not being properly created or updated in the `user_profiles` table. This causes several issues:

1. Profile information is not displayed correctly
2. Profile updates appear to save but are lost on refresh
3. Console errors show "Error fetching user profile: The result contains 0 rows"

## The Solution

We've implemented a comprehensive fix that addresses these issues at multiple levels:

1. Database level: Fixed the trigger function and RLS policies
2. Service level: Added robust error handling and fallback mechanisms
3. UI level: Improved error handling and user feedback

## How to Apply the Fix

### Step 1: Run the SQL Migration

1. Go to the Supabase Dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of the `src/db/migrations/fix_user_profile_system.sql` file
4. Paste it into the SQL Editor and run it

This script will:
- Create the `user_profiles` table if it doesn't exist
- Add all required columns
- Fix the RLS policies to ensure users can access their profiles
- Fix the trigger function that creates profiles when users register
- Create profiles for existing users who don't have one

### Step 2: Verify the Code Changes

Make sure the following files have been updated:

1. `src/services/userProfileService.ts` - Added robust error handling and fallback mechanisms
2. `src/pages/Profile.tsx` - Improved error handling and user feedback
3. `src/context/SupabaseAuthContext.tsx` - Added fallback for profile creation during registration

### Step 3: Test the System

After applying the fixes, test the user profile system:

1. Register a new user
2. Log in with the new user
3. Go to the profile page
4. Update the profile information
5. Refresh the page to verify the changes persist

## Technical Details

### Database Changes

1. **Fixed User Profiles Table**:
   - Ensured all required columns exist
   - Added proper constraints and defaults

2. **Fixed RLS Policies**:
   - Simplified policies to avoid recursion
   - Added policies for users to view and update their own profiles
   - Added policies for admins to manage all profiles

3. **Fixed Trigger Function**:
   - Added error handling to prevent registration failures
   - Added checks to prevent duplicate key errors
   - Added fallback for missing data

### Code Changes

1. **User Profile Service**:
   - Added `createUserProfile` function as a fallback
   - Enhanced error handling in `getUserProfile`
   - Improved `updateUserProfile` with upsert capability

2. **Profile Page**:
   - Added fallback for missing profile data
   - Improved error handling and user feedback
   - Added detailed logging for troubleshooting

3. **Auth Context**:
   - Added fallback for profile creation during registration
   - Improved error handling for database errors

## Troubleshooting

If you still encounter issues:

1. **Check Console Errors**: Look for specific error messages in the browser console
2. **Verify Database Structure**: Make sure the `user_profiles` table has all required columns
3. **Check RLS Policies**: Ensure the policies allow users to access their profiles
4. **Verify Trigger Function**: Make sure the trigger function is correctly creating profiles

### Manual Profile Creation

If needed, you can manually create a profile for a user:

```sql
INSERT INTO user_profiles (
  id, 
  display_name, 
  email, 
  role,
  created_at,
  updated_at
)
VALUES (
  'user-id-here',  -- Replace with the actual user ID
  'User Name',     -- Replace with the user's name
  '<EMAIL>', -- Replace with the user's email
  'user',
  now(),
  now()
)
ON CONFLICT (id) DO UPDATE SET
  display_name = EXCLUDED.display_name,
  email = EXCLUDED.email,
  updated_at = now();
```

## Need Help?

If you continue to experience issues, please contact the development team with:
- The specific error messages from the browser console
- The user ID that's experiencing the issue
- Screenshots of the profile page
- The time when the error occurred
