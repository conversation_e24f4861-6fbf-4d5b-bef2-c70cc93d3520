import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

/**
 * Auth Callback Page
 * 
 * This page handles the OAuth callback from providers like Google.
 * It processes the authentication response and redirects the user.
 */
const AuthCallback = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // The hash contains the token information
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }

        if (data?.session) {
          // Successfully authenticated
          toast({
            title: "Login successful",
            description: "You have successfully signed in",
          });
          
          // Redirect to home page or a protected route
          navigate('/');
        } else {
          // No session found, might be an error
          setError("Authentication failed. Please try again.");
          setTimeout(() => navigate('/login'), 3000);
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError(err instanceof Error ? err.message : "Authentication failed");
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-md w-full">
          <h1 className="text-lg font-medium text-red-800 mb-2">Authentication Error</h1>
          <p className="text-red-700">{error}</p>
          <p className="text-sm text-red-600 mt-2">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="flex flex-col items-center">
        <Loader2 className="h-12 w-12 animate-spin text-badhees-600 mb-4" />
        <h1 className="text-xl font-bold text-badhees-800 mb-2">Completing Sign In</h1>
        <p className="text-badhees-600">Please wait while we authenticate you...</p>
      </div>
    </div>
  );
};

export default AuthCallback;
