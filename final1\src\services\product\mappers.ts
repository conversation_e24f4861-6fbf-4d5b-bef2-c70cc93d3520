/**
 * Product Mappers
 *
 * This module provides functions to transform product data between different formats.
 */
import { FrontendProduct, SupabaseProduct } from './types';

/**
 * Convert Supabase product to frontend product format
 * @param product The Supabase product to convert
 * @returns Frontend product representation
 */
export const mapSupabaseProductToFrontend = (product: SupabaseProduct): FrontendProduct => {
  // Find primary image
  const primaryImage = product.images?.find(img => img.is_primary)?.image_url || '';

  // Get all image URLs
  const allImages = product.images?.map(img => img.image_url) || [];

  // Specifications are already in the correct format as a Record<string, string>
  const specifications = product.specifications || {};

  // Get rating information - prioritize direct columns, fall back to rating_summary
  let rating = 0;
  let reviewCount = 0;

  // First check if the product has direct rating and review_count columns
  if (product.rating !== undefined && product.rating !== null) {
    rating = parseFloat(String(product.rating)) || 0;
  } else if (product.rating_summary) {
    rating = parseFloat(product.rating_summary.average_rating) || 0;
  }

  if (product.review_count !== undefined && product.review_count !== null) {
    reviewCount = parseInt(String(product.review_count)) || 0;
  } else if (product.rating_summary) {
    reviewCount = parseInt(product.rating_summary.review_count) || 0;
  }

  // Log the rating data for debugging
  console.log(`Product ${product.id} (${product.name}) rating data:`, {
    directRating: product.rating,
    directReviewCount: product.review_count,
    summaryRating: product.rating_summary?.average_rating,
    summaryReviewCount: product.rating_summary?.review_count,
    finalRating: rating,
    finalReviewCount: reviewCount
  });

  return {
    id: product.id,
    name: product.name,
    description: product.description || '',
    price: product.price,
    salePrice: product.sale_price,
    isSale: product.is_sale,
    isNew: product.is_new,
    isFeatured: product.is_featured || false,
    image: primaryImage,
    images: allImages,
    category: product.category?.name || '',
    status: product.status,
    stock: product.stock,
    sku: product.sku || '',
    specifications: Object.keys(specifications).length > 0 ? specifications : undefined,
    customizationAvailable: product.customization_available || false,
    rating: rating,
    reviewCount: reviewCount,
    created_at: product.created_at,
    updated_at: product.updated_at
  };
};
