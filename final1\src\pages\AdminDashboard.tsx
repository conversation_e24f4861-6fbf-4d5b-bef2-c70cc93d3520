
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { ChevronRight, PackageOpen } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { toast } from 'sonner';
import AdminDashboardStats from '@/components/admin/AdminDashboardStats';
import { useAuth } from '@/context/SupabaseAuthContext';
import { getProducts } from '@/services/supabaseProductsService';
import AdminSidebar from '@/components/admin/AdminSidebar';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

// Define the AdminProduct type which matches what AdminProductTable expects
interface AdminProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number;
  isSale?: boolean;
  isNew?: boolean;
  image: string;
  category: string;
  stock?: number;
  status?: 'active' | 'draft' | 'deleted';
}

const AdminDashboard = () => {
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { isAuthenticated, user, isAdmin } = useAuth();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Fetch products from Supabase
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const supabaseProducts = await getProducts();

        // Map Supabase products to AdminProduct format
        const formattedProducts = supabaseProducts.map(product => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          salePrice: product.sale_price,
          isSale: product.is_sale,
          isNew: product.is_new,
          image: product.images?.[0]?.image_url || 'https://placehold.co/100x100?text=No+Image',
          category: product.category?.name || 'Uncategorized',
          stock: product.stock || 0,
          status: product.status
        }));

        setProducts(formattedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast.error('Failed to load products');
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && isAdmin()) {
      fetchProducts();
    }
  }, [isAuthenticated, isAdmin]);

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin');
      toast.error('Please login to access the admin dashboard');
    } else if (!isAdmin()) {
      navigate('/');
      toast.error('You do not have admin privileges');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <Link to="/login?redirect=/admin">Login</Link>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex flex-1 pt-20">
        <AdminSidebar />

        <div className="flex-1 p-6 overflow-auto bg-gray-50">
          {/* Breadcrumb */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Admin Dashboard</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-8">
            Welcome to your Dashboard
          </h1>

          {/* Dashboard Overview */}
          <AdminDashboardStats products={products} />

          {/* Quick Links Section */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Quick link cards */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Products</CardTitle>
                  <CardDescription>Manage your store's products</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>Add, edit, or remove products from your inventory.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/products" className="flex justify-between items-center w-full">
                      <span>Manage Products</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Custom Projects</CardTitle>
                  <CardDescription>Showcase your completed work</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>Add and manage your custom interior projects.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/completed-projects" className="flex justify-between items-center w-full">
                      <span>Manage Projects</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Orders</CardTitle>
                  <CardDescription>Process customer orders</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>View and update order statuses and details.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/orders" className="flex justify-between items-center w-full">
                      <span>Process Orders</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <h2 className="text-xl font-semibold mb-4 mt-8">System Tools</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Fix Product Ratings</CardTitle>
                  <CardDescription>Repair product rating issues</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>Fix product ratings that aren't displaying correctly across the site.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/fix-ratings" className="flex justify-between items-center w-full">
                      <span>Fix Ratings</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminDashboard;
