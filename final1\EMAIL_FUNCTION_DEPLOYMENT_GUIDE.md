# Email Function Deployment Guide

This guide provides step-by-step instructions for deploying the unified email service function to Supabase.

## Prerequisites

- Supabase CLI installed (`npm install -g supabase`)
- Supabase project set up
- SMTP server credentials (Gmail in this case)

## Local Development Setup

### 1. Set Environment Variables

1. Create a `.env.local` file in the `supabase` directory with the following content:

```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=psmr krvj ccdv vphq
EMAIL_FROM=<EMAIL>
SITE_URL=http://localhost:5173
```

2. Set these environment variables in your local Supabase instance:

```bash
cd final1
npx supabase secrets set --env-file ./supabase/.env.local
```

### 2. Deploy Edge Function Locally

```bash
cd final1/supabase/functions
npx supabase functions serve email-service --no-verify-jwt
```

The `--no-verify-jwt` flag allows you to test the function without authentication during development.

### 3. Test the Function

Use curl or Postman to test the function:

```bash
# Test welcome email
curl -X POST http://localhost:54321/functions/v1/email-service \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST http://localhost:54321/functions/v1/email-service \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace `YOUR_USER_ID` and `YOUR_ORDER_ID` with actual IDs from your database.

## Production Deployment

### 1. Set Environment Variables in Production

Set the environment variables in your production Supabase project:

```bash
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD="psmr krvj ccdv vphq"
npx supabase secrets set EMAIL_FROM=<EMAIL>
npx supabase secrets set SITE_URL=https://thebadhees.com
```

**Important Notes:**
- Use quotes around the password if it contains spaces
- Replace `https://thebadhees.com` with your actual production URL
- Consider using a dedicated email service like SendGrid for production

### 2. Deploy Edge Function to Production

```bash
cd final1
npx supabase functions deploy email-service --use-docker=false
```

Note: The `--use-docker=false` flag allows you to deploy without having Docker installed.

### 3. Apply Database Migrations

```bash
cd final1
npx supabase db push
```

This will apply all migrations, including the one that sets up the email logs table and triggers.

## Important Configuration Changes for Production

### 1. Update Frontend Environment Variables

In your production environment, make sure to set:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_KEY=your-service-role-key
```

The service role key is used for server-side email sending when no user session is available.

### 2. Email Security Considerations

For production:

1. **Consider using a transactional email service** like SendGrid, Mailgun, or Amazon SES instead of Gmail
2. **Rotate your email app password** periodically
3. **Set up SPF and DKIM records** for your domain to improve email deliverability
4. **Monitor email sending limits** (Gmail has a limit of 500 emails per day)

### 3. Vercel Environment Variables

If deploying to Vercel, add these environment variables in the Vercel dashboard:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_KEY=your-service-role-key
```

## Troubleshooting

### Common Issues

1. **"Docker Desktop is required" error**
   - This error appears when trying to run functions locally
   - For deployment, you don't need Docker installed
   - Make sure you're using the `deploy` command, not `serve`

2. **Emails not sending**
   - Check Edge Function logs: `npx supabase functions logs email-service`
   - Verify SMTP credentials are correct
   - Check if your email provider is blocking the connection

3. **"Cannot find module" errors**
   - Make sure you've deployed the functions with the correct dependencies
   - Check import paths in the function code

4. **Authentication errors**
   - In production, make sure you're providing a valid JWT token
   - For local testing, use the `--no-verify-jwt` flag

5. **Rate limiting**
   - Gmail limits sending to 500 emails per day
   - Consider using a dedicated email service for production

### Viewing Function Status

```bash
# List all deployed functions and their status
npx supabase functions list
```

## Email Templates

The email templates are embedded in the email-service function. To customize them:

1. Edit the templates in `supabase/functions/email-service/index.ts`
2. Re-deploy the function:
   ```bash
   npx supabase functions deploy email-service
   ```

For better organization, you can also move the templates to separate files in a templates directory:

```
supabase/functions/email-service/templates/welcome.html
supabase/functions/email-service/templates/order-confirmation.html
supabase/functions/email-service/templates/delivery-confirmation.html
```

And then update the function to read from these files.

## Monitoring Email Logs

You can view email logs in the Supabase Table Editor or with this SQL query:

```sql
SELECT
  email_logs.id,
  email_logs.email_type,
  email_logs.recipient,
  email_logs.status,
  email_logs.sent_at,
  orders.id as order_id
FROM email_logs
LEFT JOIN orders ON email_logs.order_id = orders.id
ORDER BY email_logs.sent_at DESC;
```

## Security Best Practices

1. **Never commit email credentials to your repository**
2. **Use environment variables** for all sensitive information
3. **Implement rate limiting** to prevent abuse
4. **Validate email recipients** before sending
5. **Use Row Level Security** to protect email logs
6. **Sanitize all user input** used in email templates
