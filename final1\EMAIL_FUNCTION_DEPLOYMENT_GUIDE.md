# Email Function Deployment Guide

This guide provides detailed instructions for setting up and deploying the email functions for The Badhees website, including welcome emails and order notification emails.

## Local Development Setup

### 1. Set Environment Variables

1. Create a `.env.local` file in the `supabase` directory with the following content:

```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=psmr krvj ccdv vphq
EMAIL_FROM=<EMAIL>
SITE_URL=http://localhost:5173
```

2. Set these environment variables in your local Supabase instance:

```bash
cd final1
supabase secrets set --env-file ./supabase/.env.local
```

### 2. Deploy Edge Functions Locally

```bash
cd final1/supabase/functions
supabase functions serve welcome-email --no-verify-jwt
supabase functions serve order-emails --no-verify-jwt
```

The `--no-verify-jwt` flag allows you to test the functions without authentication during development.

### 3. Test the Functions

Use curl or <PERSON><PERSON> to test the functions:

```bash
# Test welcome email
curl -X POST http://localhost:54321/functions/v1/welcome-email \
  -H "Content-Type: application/json" \
  -d '{"userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST http://localhost:54321/functions/v1/order-emails \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace `YOUR_USER_ID` and `YOUR_ORDER_ID` with actual IDs from your database.

## Production Deployment

### 1. Set Environment Variables in Production

Set the environment variables in your production Supabase project:

```bash
supabase secrets set EMAIL_HOST=smtp.gmail.com
supabase secrets set EMAIL_PORT=587
supabase secrets set EMAIL_USERNAME=<EMAIL>
supabase secrets set EMAIL_PASSWORD="psmr krvj ccdv vphq"
supabase secrets set EMAIL_FROM=<EMAIL>
supabase secrets set SITE_URL=https://thebadhees.com
```

**Important Notes:**
- Use quotes around the password if it contains spaces
- Replace `https://thebadhees.com` with your actual production URL
- Consider using a dedicated email service like SendGrid for production

### 2. Deploy Edge Functions to Production

```bash
cd final1/supabase/functions
supabase functions deploy welcome-email
supabase functions deploy order-emails
```

### 3. Apply Database Migrations

```bash
cd final1
supabase db push
```

This will apply all migrations, including the one that sets up the email logs table and triggers.

## Important Configuration Changes for Production

### 1. Update Frontend Environment Variables

In your production environment, make sure to set:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_KEY=your-service-role-key
```

The service role key is used for server-side email sending when no user session is available.

### 2. Email Security Considerations

For production:

1. **Consider using a transactional email service** like SendGrid, Mailgun, or Amazon SES instead of Gmail
2. **Rotate your email app password** periodically
3. **Set up SPF and DKIM records** for your domain to improve email deliverability
4. **Monitor email sending limits** (Gmail has a limit of 500 emails per day)

### 3. Vercel Environment Variables

If deploying to Vercel, add these environment variables in the Vercel dashboard:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_KEY=your-service-role-key
```

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check Edge Function logs: `supabase functions logs welcome-email`
   - Verify SMTP credentials are correct
   - Check if your email provider is blocking the connection

2. **"Cannot find module" errors**
   - Make sure you've deployed the functions with the correct dependencies
   - Check import paths in the function code

3. **Authentication errors**
   - In production, make sure you're providing a valid JWT token
   - For local testing, use the `--no-verify-jwt` flag

4. **Rate limiting**
   - Gmail limits sending to 500 emails per day
   - Consider using a dedicated email service for production

### Viewing Logs

```bash
# View logs for welcome email function
supabase functions logs welcome-email

# View logs for order emails function
supabase functions logs order-emails
```

## Email Templates

The email templates are located at:

- `supabase/functions/welcome-email/templates/welcome.html`
- `supabase/functions/order-emails/templates/order-confirmation.html`
- `supabase/functions/order-emails/templates/delivery-confirmation.html`

To customize these templates:

1. Edit the HTML files
2. Re-deploy the functions:
   ```bash
   supabase functions deploy welcome-email
   supabase functions deploy order-emails
   ```

## Monitoring Email Logs

You can view email logs in the Supabase Table Editor or with this SQL query:

```sql
SELECT 
  email_logs.id,
  email_logs.email_type,
  email_logs.recipient,
  email_logs.status,
  email_logs.sent_at,
  orders.id as order_id
FROM email_logs
LEFT JOIN orders ON email_logs.order_id = orders.id
ORDER BY email_logs.sent_at DESC;
```

## Security Best Practices

1. **Never commit email credentials to your repository**
2. **Use environment variables** for all sensitive information
3. **Implement rate limiting** to prevent abuse
4. **Validate email recipients** before sending
5. **Use Row Level Security** to protect email logs
6. **Sanitize all user input** used in email templates
