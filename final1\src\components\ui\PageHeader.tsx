/**
 * Page Header Component
 * 
 * A consistent header component for page titles and descriptions.
 */
import React from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ 
  title, 
  description, 
  className, 
  ...props 
}) => {
  return (
    <div className={cn("mb-8", className)} {...props}>
      <h1 className="text-2xl font-bold tracking-tight text-badhees-800 sm:text-3xl">
        {title}
      </h1>
      {description && (
        <p className="mt-2 text-lg text-badhees-600">
          {description}
        </p>
      )}
    </div>
  );
};
