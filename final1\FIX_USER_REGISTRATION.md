# Fix User Registration Issues

This guide explains how to fix the user registration issues in the Supabase backend.

## The Problem

When users try to register, they may encounter a "Database error saving new user" message. This happens because:

1. The database trigger function `handle_new_user` that creates a user profile when a new user signs up is failing
2. The `email_logs` table might not exist or have the correct permissions
3. There are issues with the Row Level Security (RLS) policies

## The Solution

We've created a SQL migration script that fixes these issues. Here's how to apply it:

### Step 1: Run the SQL Migration

1. Go to the Supabase Dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of the `src/db/migrations/fix_user_registration.sql` file
4. Paste it into the SQL Editor and run it

This script will:
- Fix the `handle_new_user` trigger function to be more robust
- Create the `email_logs` table if it doesn't exist
- Set up proper RLS policies
- Add error handling to prevent registration failures

### Step 2: Test User Registration

After applying the SQL migration, test the user registration process:

1. Log out if you're currently logged in
2. Go to the registration page
3. Fill in the registration form with a new email and password
4. Submit the form
5. Verify that the registration completes successfully

### Step 3: Verify User Profile Creation

To verify that user profiles are being created correctly:

1. Go to the Supabase Dashboard
2. Navigate to Table Editor
3. Select the `user_profiles` table
4. Check that the newly registered user has a profile entry

## Manual Fix (If Needed)

If you still encounter issues, you can manually create user profiles for existing users:

```sql
-- For a specific user
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
VALUES (
  'user-id-here',
  'User Name',
  '<EMAIL>',
  'user',
  now(),
  now()
)
ON CONFLICT (id) DO NOTHING;

-- For all users without profiles
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;
```

## Fallback Mechanism

We've also added a fallback mechanism in the code that will:

1. Attempt to create a user profile manually if the database trigger fails
2. Handle database errors gracefully with user-friendly error messages
3. Still allow users to register even if there are issues with the profile creation

This ensures that users can register successfully even if there are temporary database issues.

## Troubleshooting

If you continue to experience issues:

1. Check the browser console for specific error messages
2. Look at the Supabase logs for database errors
3. Verify that the RLS policies are set up correctly
4. Make sure the `user_profiles` table has the correct structure
5. Ensure that the `auth.users` table is accessible to the trigger function

## Need Help?

If you need further assistance, please contact the development team with:
- The specific error message you're seeing
- The email address you're trying to register with
- Screenshots of any error messages
- The time when the error occurred
