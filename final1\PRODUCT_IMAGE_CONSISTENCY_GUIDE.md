# Product Image Consistency Guide

This guide provides instructions for maintaining consistent product images and layouts throughout the application.

## Image Requirements

### Product Image Specifications

For all product images, please follow these specifications:

1. **Aspect Ratio**: All product images should be square (1:1 aspect ratio)
2. **Resolution**: Minimum 800x800 pixels, recommended 1200x1200 pixels
3. **Format**: JPEG or PNG (PNG preferred for products with transparency)
4. **File Size**: Optimize images to be under 200KB each
5. **Background**: Consistent background (white or transparent preferred)
6. **Lighting**: Consistent lighting across all product images
7. **Positioning**: Center the product in the frame with appropriate padding

### Image Preparation

Before uploading product images:

1. **Crop to Square**: Ensure all images are cropped to a perfect square
2. **Resize**: Resize to the recommended dimensions (1200x1200px)
3. **Optimize**: Compress images without losing quality
4. **Naming Convention**: Use consistent naming: `product-id-main.jpg`, `product-id-1.jpg`, etc.

## Adding New Products

When adding new products to the system:

1. **Main Image**: Always provide a high-quality main image that follows the specifications
2. **Multiple Views**: Include multiple images showing different angles of the product
3. **Detail Shots**: Add close-up images of important details or features
4. **Context Images**: Consider including images showing the product in context/use

## Technical Implementation

The application now includes several features to ensure consistent product display:

1. **Forced Aspect Ratio**: All product images are displayed in a 1:1 aspect ratio container
2. **Object-Fit Cover**: Images maintain their aspect ratio and fill the container
3. **Consistent Card Heights**: Product cards maintain consistent heights
4. **Responsive Design**: Images adapt to different screen sizes while maintaining consistency

## CSS Classes

Use these CSS classes to maintain consistency:

```css
/* For product image containers */
.product-image-container {
  aspect-ratio: 1/1;
  overflow: hidden;
}

/* For product images */
.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
```

## Share Functionality

The product cards now include a share button instead of a wishlist button. This allows users to:

1. Share products via the Web Share API on supported devices
2. Copy the product URL to clipboard on devices without Web Share API support

### How the Share Feature Works

When a user clicks the share icon:

1. The application attempts to use the Web Share API
2. If available, it opens the device's native share dialog
3. If not available, it copies the product URL to the clipboard
4. A toast notification confirms the action

## Best Practices

1. **Batch Process Images**: Use image editing tools to batch process multiple images
2. **Quality Control**: Review all images before uploading to ensure consistency
3. **Mobile Testing**: Test how images appear on mobile devices
4. **Performance**: Monitor page load times and optimize images if needed

## Troubleshooting

If product images appear inconsistent:

1. **Check Aspect Ratio**: Ensure the image is properly cropped to a square
2. **Verify Dimensions**: Confirm the image meets the minimum size requirements
3. **Inspect CSS**: Check if any custom styles are overriding the consistency rules
4. **Clear Cache**: Have users clear their browser cache if they see outdated images

## Tools for Image Preparation

- **Adobe Photoshop**: Professional image editing
- **GIMP**: Free alternative to Photoshop
- **Squoosh.app**: Browser-based image compression
- **TinyPNG**: Efficient PNG compression
- **ImageOptim**: Batch image optimization (Mac)
- **FileOptimizer**: Batch image optimization (Windows)

By following these guidelines, you'll ensure a consistent and professional appearance for all products across the application.
