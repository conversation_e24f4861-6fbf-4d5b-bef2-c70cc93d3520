# Supabase Email Verification Configuration Guide

This guide will help you fix the email verification issues and set up proper email templates for The Badhees website.

## Problem Summary

Currently, when users sign up, they receive a generic Supabase email that:
1. Links to `localhost` which doesn't work in production
2. Has no branding or company information
3. Causes "refused to connect" errors

## Solution Overview

We need to:
1. Configure proper redirect URLs in Supabase
2. Set up custom email templates
3. Configure the Site URL correctly

## Step 1: Configure Supabase Authentication Settings

### 1.1 Set Site URL
1. Go to your Supabase project dashboard
2. Navigate to **Authentication > URL Configuration**
3. Set the **Site URL** to your production domain:
   - For production: `https://your-domain.com`
   - For local development: `http://localhost:5173`

### 1.2 Configure Redirect URLs
Add these URLs to the **Redirect URLs** section:
```
https://your-domain.com
https://your-domain.com/confirm-email
https://your-domain.com/auth/callback
https://your-domain.com/reset-password
http://localhost:5173 (for local development)
http://localhost:5173/confirm-email (for local development)
http://localhost:5173/auth/callback (for local development)
```

## Step 2: Configure Custom Email Templates

### 2.1 Access Email Templates
1. In your Supabase dashboard, go to **Authentication > Email Templates**
2. Select **Confirm signup** template

### 2.2 Update the Email Template
Replace the default template with the custom HTML template provided in `email-templates/email-confirmation.html`.

**Important Configuration:**
- Set the **Redirect URL** to: `{{ .SiteURL }}/confirm-email?token={{ .TokenHash }}&type=signup`
- This will redirect users to our custom confirmation page instead of the default Supabase page

### 2.3 Template Variables
The template uses these Supabase variables:
- `{{ .ConfirmationURL }}` - The confirmation link
- `{{ .SiteURL }}` - Your site URL
- `{{ .TokenHash }}` - The verification token

## Step 3: Environment Variables

Make sure your environment variables are set correctly:

### For Production (Vercel/Netlify):
```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### For Local Development (.env.local):
```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## Step 4: Test the Email Flow

### 4.1 Local Testing
1. Start your development server: `npm run dev`
2. Register a new user
3. Check your email for the confirmation message
4. Click the confirmation link
5. Verify you're redirected to the custom confirmation page

### 4.2 Production Testing
1. Deploy your changes to production
2. Update the Site URL in Supabase to your production domain
3. Test the complete flow with a new email address

## Step 5: Additional Email Templates (Optional)

You can also customize other email templates:

### Password Reset Email
- Template: **Reset password**
- Redirect URL: `{{ .SiteURL }}/reset-password?token={{ .TokenHash }}&type=recovery`

### Magic Link Email
- Template: **Magic Link**
- Redirect URL: `{{ .SiteURL }}/auth/callback`

## Troubleshooting

### Issue: "This site can't be reached" or "localhost refused to connect"
**Solution:** Make sure the Site URL in Supabase matches your actual domain (not localhost for production).

### Issue: Email not being sent
**Solution:** 
1. Check if email confirmation is enabled in Authentication settings
2. Verify your email template is saved correctly
3. Check Supabase logs for any errors

### Issue: Confirmation link doesn't work
**Solution:**
1. Verify the redirect URL format in the email template
2. Make sure the `/confirm-email` route exists in your app
3. Check that the token is being passed correctly

### Issue: Users not being marked as confirmed
**Solution:**
1. Check the `EmailConfirmation.tsx` component is handling the verification correctly
2. Verify the Supabase client is configured properly
3. Check browser console for any JavaScript errors

## Security Considerations

1. **Token Expiration**: Email confirmation tokens expire after 24 hours by default
2. **HTTPS Only**: Always use HTTPS in production for security
3. **Domain Validation**: Only add trusted domains to redirect URLs
4. **Rate Limiting**: Supabase has built-in rate limiting for email sending

## Support

If you encounter issues:
1. Check the Supabase dashboard logs
2. Verify all URLs are correctly configured
3. Test with different email providers (Gmail, Outlook, etc.)
4. Contact Supabase support if the issue persists

## Next Steps

After implementing these changes:
1. Test the complete user registration flow
2. Monitor email delivery rates
3. Consider implementing welcome emails after confirmation
4. Set up email analytics if needed
