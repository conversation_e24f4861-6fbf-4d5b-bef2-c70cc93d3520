@echo off
echo Setting up email functions...

REM Check if .env.local exists
if not exist "supabase\.env.local" (
    echo Error: supabase\.env.local file not found!
    echo Please create this file with your email configuration.
    exit /b 1
)

REM Set environment variables from .env.local
echo Setting environment variables from .env.local file...
npx supabase secrets set --env-file supabase\.env.local

REM Start the functions locally
echo Starting email functions locally...
cd supabase\functions
echo Starting welcome-email function...
start cmd /k npx supabase functions serve welcome-email --no-verify-jwt
echo Starting order-emails function...
start cmd /k npx supabase functions serve order-emails --no-verify-jwt

echo Email functions are now running locally!
echo Welcome email: http://localhost:54321/functions/v1/welcome-email
echo Order emails: http://localhost:54321/functions/v1/order-emails
