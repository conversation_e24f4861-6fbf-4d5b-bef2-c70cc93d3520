-- Create email_logs table to track sent emails
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  email_type TEXT NOT NULL,
  recipient TEXT NOT NULL,
  status TEXT NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_email_logs_order_id ON email_logs(order_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);

-- Set up Row Level Security (RLS)
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_logs
-- <PERSON><PERSON> can view all email logs
CREATE POLICY "<PERSON><PERSON> can view all email logs"
  ON email_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Users can view their own email logs
CREATE POLICY "Users can view their own email logs"
  ON email_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM orders
      WHERE orders.id = email_logs.order_id
      AND orders.user_id = auth.uid()
    )
  );

-- Only the system can insert email logs
CREATE POLICY "System can insert email logs"
  ON email_logs FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL);

-- Add comments to explain the table
COMMENT ON TABLE email_logs IS 'Tracks emails sent to users for order notifications';
COMMENT ON COLUMN email_logs.email_type IS 'Type of email (order_confirmation, delivery_confirmation, etc.)';
COMMENT ON COLUMN email_logs.status IS 'Status of the email (sent, failed, etc.)';
