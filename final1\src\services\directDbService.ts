import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/use-toast';

/**
 * Interface for user profile data
 */
export interface UserProfile {
  id: string;
  display_name?: string;
  email?: string;
  role?: string;
  phone?: string;
  dob?: string;
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Direct database access for updating user profiles
 * This bypasses RLS by using a stored procedure with SECURITY DEFINER
 * 
 * @param userId The user ID to update the profile for
 * @param profileData The profile data to update
 * @returns True if successful, false otherwise
 */
export const directUpdateUserProfile = async (
  userId: string,
  profileData: Partial<UserProfile>
): Promise<boolean> => {
  try {
    console.log('Direct DB update for user:', userId, 'with data:', profileData);
    
    // Call the stored procedure with SECURITY DEFINER
    const { data, error } = await supabase.rpc('update_user_profile', {
      user_id: userId,
      p_display_name: profileData.display_name,
      p_phone: profileData.phone,
      p_dob: profileData.dob ? new Date(profileData.dob) : null,
      p_street: profileData.street,
      p_city: profileData.city,
      p_state: profileData.state,
      p_postal_code: profileData.postal_code,
      p_country: profileData.country,
      p_avatar_url: profileData.avatar_url
    });
    
    if (error) {
      console.error('Error in direct DB update:', error);
      toast({
        title: 'Error updating profile',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }
    
    console.log('Direct DB update successful:', data);
    toast({
      title: 'Profile updated',
      description: 'Your profile has been successfully updated',
    });
    
    return true;
  } catch (error: any) {
    console.error('Exception in directUpdateUserProfile:', error);
    toast({
      title: 'Error updating profile',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Ensures a user profile exists by calling the database function
 * 
 * @param userId The user ID to ensure a profile for
 * @returns True if successful, false otherwise
 */
export const ensureUserProfile = async (userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('ensure_user_profile', {
      user_id: userId
    });
    
    if (error) {
      console.error('Error ensuring user profile:', error);
      return false;
    }
    
    return data === true;
  } catch (error) {
    console.error('Exception in ensureUserProfile:', error);
    return false;
  }
};
