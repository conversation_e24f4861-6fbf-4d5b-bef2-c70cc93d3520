# Local Email Testing Guide

This guide explains how to test the email functionality in your local development environment.

## Prerequisites

1. Supabase CLI installed
2. Access to the project's Supabase account
3. Gmail account with App Password configured

## Common Issues and Solutions

Based on the console errors, here are the main issues that might prevent emails from being sent:

1. **Missing Authentication**: The Edge Function requires proper authentication
2. **Missing SMTP Credentials**: Email server credentials must be properly configured
3. **Database Relationship Issues**: Errors with user_profiles join can prevent fetching user data
4. **Environment Variables**: The SITE_URL must be set correctly for local testing

## Setting Up for Local Testing

### 1. Update Environment Variables for Local Testing

```bash
# Set SITE_URL to your local development server
npx supabase secrets set SITE_URL=http://localhost:5173

# Make sure all required email credentials are set
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD=your_app_password
npx supabase secrets set EMAIL_FROM=<EMAIL>
```

### 2. Test the Email Function

You can test the email function by making API calls to the deployed function endpoint. Here's how to do it:

#### Using the Frontend

The frontend already has the necessary code to call the email service. When you:
- Register a new user (welcome email)
- Complete an order (order confirmation email)
- Change an order status to "delivered" in the admin panel (delivery confirmation email)

The system will automatically trigger the appropriate emails.

#### Manual Testing with cURL

You can also test the function directly using cURL:

```bash
# Get your JWT token
TOKEN=$(npx supabase functions invoke auth-token --no-interactive)

# Test welcome email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'

# Test delivery confirmation email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"delivery_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace `YOUR_USER_ID` and `YOUR_ORDER_ID` with actual IDs from your database.

## Troubleshooting

### Email Not Being Sent

1. Check if the function is deployed correctly:
```bash
npx supabase functions list
```

2. Verify that all environment variables are set:
```bash
npx supabase secrets list
```

3. Check for errors in the function logs (in Supabase Dashboard)

### Authentication Errors

If you see "No authentication method available for sending email":

1. Make sure you have a valid service key:
   ```bash
   # Add this to your .env file
   VITE_SUPABASE_SERVICE_KEY=your_service_role_key
   ```

2. For admin operations, make sure you're logged in with an admin account

3. Try using a direct JWT token for testing:
   ```bash
   # Get a token and use it for authentication
   TOKEN=$(npx supabase functions invoke auth-token --no-interactive)
   ```

### Database Relationship Errors

If you see "Error with user_profiles join" messages:

1. Check that the user_profiles table has the correct foreign key relationship with auth.users
2. Make sure the user_id in orders table exists in user_profiles
3. Try the fallback method that's already implemented in the code

### Gmail App Password Issues

If you're having issues with Gmail authentication:

1. Make sure you've created an App Password in your Google Account
2. Verify that the EMAIL_USERNAME and EMAIL_PASSWORD are set correctly
3. Try using a different email service if Gmail continues to cause issues

### Testing Specific Email Types

For testing specific email types with the reference user ID:

1. Welcome Email:
   ```bash
   curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"type":"welcome_email","userId":"1ddf5ad6-54dc-4ef5-b186-314a066ad43c"}'
   ```

2. Order Confirmation (replace with an actual order ID):
   ```bash
   curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'
   ```

## Preparing for Production Deployment

Before deploying to production:

1. Update the SITE_URL to your production URL:
```bash
npx supabase secrets set SITE_URL=https://thebadhees.com
```

2. Make sure all email credentials are properly set in production:
```bash
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD=your_app_password
npx supabase secrets set EMAIL_FROM=<EMAIL>
```

3. Consider using a dedicated email service like SendGrid or Mailgun for production use

4. Test the email functionality thoroughly in the production environment after deployment

5. Set up proper error monitoring and logging for the email service

## Fixing the Current Issues

Based on the console errors, here are the specific fixes needed:

1. **Authentication Error**: Add a valid service key to your environment variables
   ```bash
   # Add to your .env file
   VITE_SUPABASE_SERVICE_KEY=your_service_role_key
   ```

2. **Database Relationship Error**: The code already has fallback mechanisms for this, but you can fix it by ensuring proper relationships in your database schema

3. **Edge Function Error**: Make sure all environment variables are properly set and the Edge Function is deployed correctly

4. **HTML Template Issues**: The HTML templates have been fixed to comply with standards (lang attribute added, inline styles removed)
