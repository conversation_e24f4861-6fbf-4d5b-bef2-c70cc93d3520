# Local Email Testing Guide

This guide explains how to test the email functionality in your local development environment.

## Prerequisites

1. Supabase CLI installed
2. Access to the project's Supabase account
3. Gmail account with App Password configured

## Setting Up for Local Testing

### 1. Update Environment Variables for Local Testing

```bash
# Set SITE_URL to your local development server
npx supabase secrets set SITE_URL=http://localhost:5173
```

### 2. Test the Email Function

You can test the email function by making API calls to the deployed function endpoint. Here's how to do it:

#### Using the Frontend

The frontend already has the necessary code to call the email service. When you:
- Register a new user (welcome email)
- Complete an order (order confirmation email)
- Change an order status to "delivered" in the admin panel (delivery confirmation email)

The system will automatically trigger the appropriate emails.

#### Manual Testing with cURL

You can also test the function directly using cURL:

```bash
# Get your JWT token
TOKEN=$(npx supabase functions invoke auth-token --no-interactive)

# Test welcome email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'

# Test delivery confirmation email
curl -X POST "https://tfvbwveohcbghqmxnpbd.supabase.co/functions/v1/email-service" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"delivery_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace `YOUR_USER_ID` and `YOUR_ORDER_ID` with actual IDs from your database.

## Troubleshooting

### Email Not Being Sent

1. Check if the function is deployed correctly:
```bash
npx supabase functions list
```

2. Verify that all environment variables are set:
```bash
npx supabase secrets list
```

3. Check for errors in the function logs (in Supabase Dashboard)

### Gmail App Password Issues

If you're having issues with Gmail authentication:

1. Make sure you've created an App Password in your Google Account
2. Verify that the EMAIL_USERNAME and EMAIL_PASSWORD are set correctly
3. Try using a different email service if Gmail continues to cause issues

## Preparing for Production Deployment

Before deploying to production:

1. Update the SITE_URL to your production URL:
```bash
npx supabase secrets set SITE_URL=https://thebadhees.com
```

2. Consider using a dedicated email service like SendGrid or Mailgun for production use
3. Test the email functionality thoroughly in the production environment after deployment
