// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { corsHeaders } from "../_shared/cors.ts"
import Handlebars from 'https://esm.sh/handlebars@4.7.7'
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts"

// Email configuration
const EMAIL_HOST = Deno.env.get('EMAIL_HOST') || 'smtp.gmail.com'
const EMAIL_PORT = parseInt(Deno.env.get('EMAIL_PORT') || '587')
const EMAIL_USERNAME = Deno.env.get('EMAIL_USERNAME') || ''
const EMAIL_PASSWORD = Deno.env.get('EMAIL_PASSWORD') || ''
const EMAIL_FROM = Deno.env.get('EMAIL_FROM') || '<EMAIL>'
const SITE_URL = Deno.env.get('SITE_URL') || 'https://thebadhees.com'
const LOGO_URL = `${SITE_URL}/logo.png`

// Read email templates
const orderConfirmationTemplate = await Deno.readTextFile('./templates/order-confirmation.html')
const deliveryConfirmationTemplate = await Deno.readTextFile('./templates/delivery-confirmation.html')

// Compile templates
const compileOrderConfirmation = Handlebars.compile(orderConfirmationTemplate)
const compileDeliveryConfirmation = Handlebars.compile(deliveryConfirmationTemplate)

// Initialize SMTP client
const client = new SmtpClient()

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '')

    // Create a Supabase client with the token
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: `Bearer ${token}` } } }
    )

    // Get the current user
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token)
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Parse the request body
    const { type, orderId } = await req.json()

    // Validate the request
    if (!type || !orderId) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Fetch the order details
    const { data: order, error: orderError } = await supabaseClient
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(name, price)
        ),
        user:user_profiles(*)
      `)
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      return new Response(JSON.stringify({ error: 'Order not found', details: orderError }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Fetch the shipping address
    const { data: shippingAddress, error: addressError } = await supabaseClient
      .from('addresses')
      .select('*')
      .eq('id', order.shipping_address_id)
      .single()

    if (addressError) {
      console.error('Error fetching shipping address:', addressError)
    }

    // Connect to the SMTP server
    await client.connectTLS({
      hostname: EMAIL_HOST,
      port: EMAIL_PORT,
      username: EMAIL_USERNAME,
      password: EMAIL_PASSWORD,
    })

    let emailHtml = ''
    let emailSubject = ''
    let emailSent = false

    // Format order items for the email
    const items = order.order_items.map((item: any) => ({
      name: item.product.name,
      quantity: item.quantity,
      price: (item.price * item.quantity).toFixed(2)
    }))

    // Calculate totals
    const subtotal = order.subtotal.toFixed(2)
    const shipping = order.shipping_cost.toFixed(2)
    const discount = order.discount ? order.discount.toFixed(2) : 0
    const total = order.total.toFixed(2)

    // Format dates
    const orderDate = new Date(order.created_at).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
    
    const deliveryDate = new Date().toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    // Get current year for copyright
    const currentYear = new Date().getFullYear()

    // Prepare common template data
    const templateData = {
      customerName: order.user.display_name || 'Valued Customer',
      customerEmail: order.user.email,
      orderNumber: order.id,
      orderDate,
      items,
      subtotal,
      shipping,
      discount,
      total,
      shippingAddress: shippingAddress || {},
      trackingUrl: `${SITE_URL}/orders/${order.id}`,
      reviewUrl: `${SITE_URL}/orders/${order.id}?review=true`,
      logoUrl: LOGO_URL,
      currentYear
    }

    // Send the appropriate email based on the type
    if (type === 'order_confirmation') {
      // Only send if the order status is 'pending' or 'processing'
      if (order.status !== 'pending' && order.status !== 'processing') {
        return new Response(JSON.stringify({ error: 'Order status not eligible for confirmation email' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      emailSubject = `Order Confirmation #${order.id.substring(0, 8)} - The Badhees`
      emailHtml = compileOrderConfirmation({
        ...templateData,
        paymentMethod: order.payment_method || 'Online Payment'
      })
    } else if (type === 'delivery_confirmation') {
      // Only send if the order status is 'delivered'
      if (order.status !== 'delivered') {
        return new Response(JSON.stringify({ error: 'Order status not eligible for delivery email' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      emailSubject = `Your Order Has Been Delivered! #${order.id.substring(0, 8)} - The Badhees`
      emailHtml = compileDeliveryConfirmation({
        ...templateData,
        deliveryDate
      })
    } else {
      return new Response(JSON.stringify({ error: 'Invalid email type' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Send the email
    await client.send({
      from: EMAIL_FROM,
      to: order.user.email,
      subject: emailSubject,
      html: emailHtml,
    })

    emailSent = true

    // Close the connection
    await client.close()

    // Record the email in the database
    if (emailSent) {
      await supabaseClient
        .from('email_logs')
        .insert({
          order_id: order.id,
          email_type: type,
          recipient: order.user.email,
          status: 'sent',
          sent_at: new Date().toISOString()
        })
    }

    return new Response(JSON.stringify({ success: true, message: 'Email sent successfully' }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error sending email:', error)
    
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
