
import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  ShoppingCart,
  User,
  Menu,
  X,
  Search,
  LogOut,
  Home,
  ShoppingBag,
  BookOpen,
  Grid,
  Info,
  Mail,
  Package,
  HelpCircle,
  Truck,
  FileText,
  Shield
} from "lucide-react";
import { useCart } from "@/context/SupabaseCartContext";
import { useAuth } from "@/context/SupabaseAuthContext";
import { CommandDialog } from "@/components/ui/command";
import { FrontendProduct } from "@/services/product/types";
import {
  searchProducts,
  getSearchSuggestions,
  getPopularSearches,
  getUserRecentSearches
} from "@/services/search";
import {
  SearchInput,
  SearchResults,
  SearchSuggestions,
  MobileSearchOverlay
} from "@/components/search";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [commandOpen, setCommandOpen] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);

  // Search state
  const [searchResults, setSearchResults] = useState<FrontendProduct[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [popularSearches, setPopularSearches] = useState<{ query: string; count: number }[]>([]);
  const [recentSearches, setRecentSearches] = useState<{ query: string; timestamp: Date }[]>([]);
  const [autoCompleteSuggestions, setAutoCompleteSuggestions] = useState<string[]>([]);

  const location = useLocation();
  const navigate = useNavigate();
  const { itemCount } = useCart();
  const { isAuthenticated, logout, isAdmin, user } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    setMobileMenuOpen(false);
    setSearchOpen(false);
    setCommandOpen(false);
    document.body.style.overflow = '';
  }, [location.pathname]);

  // Cleanup effect to restore body scroll when component unmounts
  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const closeMenu = () => {
    setMobileMenuOpen(false);
    // Ensure body scroll is enabled when menu is closed
    document.body.style.overflow = '';
  };

  // Toggle mobile menu with proper body scroll handling
  const toggleMobileMenu = () => {
    const newState = !mobileMenuOpen;
    setMobileMenuOpen(newState);

    // Prevent body scroll when menu is open
    document.body.style.overflow = newState ? 'hidden' : '';
  };

  // Handle search submission
  const handleSearch = async (query: string) => {
    if (!query.trim()) return;

    setIsSearching(true);
    try {
      const results = await searchProducts(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching products:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search form submission
  const handleSearchSubmit = (query: string) => {
    if (query.trim()) {
      // If we have search results and press Enter, navigate to the first result
      if (searchResults.length > 0) {
        navigate(`/products/${searchResults[0].id}`);
      } else {
        // Otherwise, navigate to search results page
        navigate(`/products?search=${encodeURIComponent(query)}`);
      }
      setSearchOpen(false);
      setCommandOpen(false);
      setMobileSearchOpen(false);
      setSearchQuery("");
    }
  };

  const handleUserIconClick = () => {
    if (isAuthenticated) {
      navigate("/profile");
    } else {
      navigate("/login");
    }
  };

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  // Load suggestions when the command dialog opens
  useEffect(() => {
    if (commandOpen) {
      const loadSuggestions = async () => {
        try {
          // Load popular searches
          const popular = await getPopularSearches(5);
          setPopularSearches(popular);

          // Load recent searches
          const recent = await getUserRecentSearches(5);
          setRecentSearches(recent);

          // If no search query, load featured products
          if (!searchQuery.trim()) {
            setIsSearching(true);
            try {
              // Use the search function with a special query to get featured products
              const featuredProducts = await searchProducts("featured");
              setSearchResults(featuredProducts.slice(0, 5));
            } finally {
              setIsSearching(false);
            }
          }
        } catch (error) {
          console.error('Error loading suggestions:', error);
        }
      };

      loadSuggestions();
    }
  }, [commandOpen]);

  // Load auto-complete suggestions when the search query changes
  useEffect(() => {
    const loadAutoCompleteSuggestions = async () => {
      if (searchQuery.trim().length < 2) {
        setAutoCompleteSuggestions([]);
        return;
      }

      try {
        const suggestions = await getSearchSuggestions(searchQuery);
        setAutoCompleteSuggestions(suggestions);
      } catch (error) {
        console.error('Error loading auto-complete suggestions:', error);
      }
    };

    if (commandOpen || searchOpen || mobileSearchOpen) {
      loadAutoCompleteSuggestions();

      // Only search if query is at least 2 characters
      if (searchQuery.trim().length >= 2) {
        handleSearch(searchQuery);
      } else {
        setSearchResults([]);
      }
    }
  }, [searchQuery, commandOpen, searchOpen, mobileSearchOpen]);

  const openCommandDialog = () => {
    setCommandOpen(true);
    setSearchOpen(false);
  };

  const getNavLinks = () => {
    const baseLinks = [
      { name: "Home", path: "/" },
      { name: "Products", path: "/shop" },
      { name: "Custom Projects", path: "/custom-interiors" },
    ];

    if (isAdmin()) {
      baseLinks.push({ name: "Dashboard", path: "/admin" });
    } else {
      baseLinks.push(
        { name: "About", path: "/about" },
        { name: "Contact", path: "/contact" }
      );
    }

    return baseLinks;
  };

  const navLinks = getNavLinks();

  return (
    <>
      <header
        className={cn(
          "fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out",
          isScrolled
            ? "py-2 sm:py-3 md:py-4 bg-white/95 backdrop-blur-md shadow-sm"
            : "py-2 sm:py-3 md:py-6 bg-white/80 backdrop-blur-sm"
        )}
      >
        <nav className="max-w-[1400px] mx-auto px-4 sm:px-6 md:px-8">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <button
                type="button"
                className="md:hidden focus-ring rounded-full p-3 hover:bg-badhees-100 touch-target mr-2"
                onClick={toggleMobileMenu}
                aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
              >
                {mobileMenuOpen ? (
                  <X className="h-6 w-6 text-badhees-600" />
                ) : (
                  <Menu className="h-6 w-6 text-badhees-600" />
                )}
              </button>
              <Link to="/" className="flex items-center">
                <h1 className="text-xl sm:text-2xl font-bold text-badhees-800 font-display">
                  The Badhees
                </h1>
              </Link>
            </div>

            <div className="hidden md:flex items-center space-x-8 lg:space-x-10">
              {navLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className={cn(
                    "text-sm font-medium transition-colors hover:text-badhees-accent",
                    location.pathname === link.path
                      ? "text-badhees-accent"
                      : "text-badhees-600"
                  )}
                >
                  {link.name}
                </Link>
              ))}
            </div>

            <div className="hidden md:flex items-center space-x-5">
              <button
                type="button"
                className="focus-ring rounded-full p-2 hover:bg-badhees-100"
                aria-label="Search"
                onClick={openCommandDialog}
              >
                <Search className="h-5 w-5 text-badhees-600" />
              </button>

              <div className="relative group">
                <button
                  type="button"
                  onClick={handleUserIconClick}
                  className="focus-ring rounded-full p-2 hover:bg-badhees-100 flex items-center"
                  aria-label="Account"
                >
                  <User className="h-5 w-5 text-badhees-600" />
                  {isAuthenticated && user && (
                    <span className="ml-2 text-sm font-medium text-badhees-600 hidden lg:inline-block">
                      {user.displayName || user.name || user.email.split('@')[0]}
                    </span>
                  )}
                </button>

                {isAuthenticated && user && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                    <div className="px-4 py-2 text-sm text-badhees-600 border-b border-gray-100">
                      <div className="font-medium truncate">{user.email}</div>
                    </div>
                    <Link to="/profile" className="block px-4 py-2 text-sm text-badhees-600 hover:bg-badhees-50">
                      Profile
                    </Link>
                    <Link to="/orders" className="block px-4 py-2 text-sm text-badhees-600 hover:bg-badhees-50">
                      Orders
                    </Link>
                    <button
                      type="button"
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-badhees-600 hover:bg-badhees-50"
                    >
                      Sign out
                    </button>
                  </div>
                )}
              </div>

              {isAuthenticated && (
                <button
                  type="button"
                  onClick={handleLogout}
                  className="focus-ring rounded-full p-2 hover:bg-badhees-100 md:hidden"
                  aria-label="Logout"
                >
                  <LogOut className="h-5 w-5 text-badhees-600" />
                </button>
              )}

              {!isAdmin() && (
                <Link
                  to="/cart"
                  className="focus-ring rounded-full p-2 hover:bg-badhees-100 relative"
                  aria-label="Cart"
                >
                  <ShoppingCart className="h-5 w-5 text-badhees-600" />
                  {itemCount > 0 && (
                    <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                      {itemCount > 99 ? '99+' : itemCount}
                    </span>
                  )}
                </Link>
              )}
            </div>

            <div className="flex md:hidden items-center space-x-3">
              <button
                type="button"
                className="focus-ring rounded-full p-3 hover:bg-badhees-100 touch-target navbar-search"
                onClick={() => setMobileSearchOpen(true)}
                aria-label="Search"
              >
                <Search className="h-6 w-6 text-badhees-600" />
              </button>

              {!isAdmin() && (
                <Link
                  to="/cart"
                  className="focus-ring rounded-full p-3 hover:bg-badhees-100 relative touch-target"
                  aria-label="Cart"
                >
                  <ShoppingCart className="h-6 w-6 text-badhees-600" />
                  {itemCount > 0 && (
                    <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                      {itemCount > 99 ? '99+' : itemCount}
                    </span>
                  )}
                </Link>
              )}
            </div>
          </div>

          {searchOpen && (
            <div className="absolute top-full left-0 right-0 bg-white shadow-md p-4 animate-fadeIn">
              <div className="relative">
                <SearchInput
                  value={searchQuery}
                  onChange={setSearchQuery}
                  onSearch={handleSearchSubmit}
                  isLoading={isSearching}
                  placeholder="Search for products..."
                  autoFocus={true}
                  className="w-full"
                />

                {searchQuery.trim().length >= 2 && (
                  <div className="absolute top-full left-0 right-0 mt-1 z-10">
                    <SearchResults
                      results={searchResults}
                      query={searchQuery}
                      isLoading={isSearching}
                      onResultSelect={(product) => {
                        navigate(`/products/${product.id}`);
                        setSearchOpen(false);
                      }}
                      onViewAllResults={() => {
                        navigate(`/products?search=${encodeURIComponent(searchQuery)}`);
                        setSearchOpen(false);
                      }}
                    />
                  </div>
                )}

                {searchQuery.trim().length < 2 && (
                  <div className="absolute top-full left-0 right-0 mt-1 z-10">
                    <SearchSuggestions
                      popularSearches={popularSearches}
                      recentSearches={recentSearches}
                      onSuggestionSelect={(query) => {
                        setSearchQuery(query);
                        handleSearch(query);
                      }}
                      currentQuery={searchQuery}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {mobileMenuOpen && (
            <div className="md:hidden fixed inset-0 top-[52px] sm:top-[60px] z-[100] bg-white shadow-lg">
              <div className="flex flex-col h-[calc(100vh-52px)] sm:h-[calc(100vh-60px)] overflow-y-auto pb-24 bg-white animate-slideInRight">
                {/* User info or Login/Register section */}
                {isAuthenticated && user ? (
                  <div className="border-b border-badhees-100 p-4">
                    <div className="flex items-center mb-2">
                      <div className="h-10 w-10 rounded-full bg-badhees-100 flex items-center justify-center text-badhees-600 font-medium mr-3">
                        {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-medium text-badhees-800">
                          {user.displayName || user.name || user.email.split('@')[0]}
                        </div>
                        <div className="text-sm text-badhees-500 truncate max-w-[200px]">
                          {user.email}
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      <Link
                        to="/profile"
                        className="py-2 px-3 text-center text-sm font-medium text-badhees-600 bg-badhees-50 rounded-md"
                        onClick={closeMenu}
                      >
                        Profile
                      </Link>
                      <button
                        type="button"
                        onClick={() => {
                          handleLogout();
                          closeMenu();
                        }}
                        className="py-2 px-3 text-center text-sm font-medium text-red-600 bg-red-50 rounded-md"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 border-b border-badhees-100">
                    <Link
                      to="/login"
                      className="py-3 px-4 text-center font-medium text-badhees-600 border-r border-badhees-100"
                      onClick={closeMenu}
                    >
                      Login
                    </Link>
                    <Link
                      to="/register"
                      className="py-3 px-4 text-center font-medium text-badhees-600"
                      onClick={closeMenu}
                    >
                      Register
                    </Link>
                  </div>
                )}

                {/* Main Menu Section */}
                <div className="py-2">
                  <Link
                    to="/"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Home className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Home</span>
                  </Link>

                  <Link
                    to="/shop"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <ShoppingBag className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Products</span>
                  </Link>

                  <Link
                    to="/custom-interiors"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <BookOpen className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Custom Projects</span>
                  </Link>

                  <Link
                    to="/categories"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Grid className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Categories</span>
                  </Link>
                </div>

                {/* Secondary Section */}
                <div className="pt-2 pb-2 px-4 bg-badhees-50 text-badhees-800 font-medium text-sm">
                  Customer Services
                </div>

                <div className="py-2">
                  <Link
                    to="/about"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Info className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">About Us</span>
                  </Link>

                  <Link
                    to="/contact"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Mail className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Contact Us</span>
                  </Link>

                  <Link
                    to="/orders"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Package className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Track Orders</span>
                  </Link>
                </div>

                {/* Help Section */}
                <div className="pt-2 pb-2 px-4 bg-badhees-50 text-badhees-800 font-medium text-sm">
                  Help & Information
                </div>

                <div className="py-2">
                  <Link
                    to="/faq"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <HelpCircle className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">FAQ</span>
                  </Link>

                  <Link
                    to="/shipping-returns"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Truck className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Shipping & Returns</span>
                  </Link>

                  <Link
                    to="/care-instructions"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <FileText className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Care Instructions</span>
                  </Link>

                  <Link
                    to="/warranty"
                    className="flex items-center py-3 px-4 text-badhees-600 hover:bg-badhees-50"
                    onClick={closeMenu}
                  >
                    <Shield className="h-5 w-5 mr-3 text-badhees-400" />
                    <span className="text-base font-medium">Warranty</span>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </nav>
      </header>

      <CommandDialog open={commandOpen} onOpenChange={setCommandOpen}>
        <div className="p-4">
          <SearchInput
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={handleSearchSubmit}
            isLoading={isSearching}
            placeholder="Search for products..."
            autoFocus={true}
            className="w-full"
          />
        </div>

        <div className="px-2 pb-4">
          {/* Show search results if we have a query and results */}
          {searchQuery.trim().length >= 2 && (
            <SearchResults
              results={searchResults}
              query={searchQuery}
              isLoading={isSearching}
              onResultSelect={(product) => {
                navigate(`/products/${product.id}`);
                setCommandOpen(false);
              }}
              onViewAllResults={() => {
                navigate(`/products?search=${encodeURIComponent(searchQuery)}`);
                setCommandOpen(false);
              }}
              maxResults={5}
              className="border-none shadow-none"
            />
          )}

          {/* Show suggestions if query is too short or we have no results */}
          {(searchQuery.trim().length < 2 || (!isSearching && searchResults.length === 0)) && (
            <SearchSuggestions
              popularSearches={popularSearches}
              recentSearches={recentSearches}
              autoCompleteSuggestions={autoCompleteSuggestions}
              onSuggestionSelect={(query) => {
                setSearchQuery(query);
                handleSearch(query);
              }}
              currentQuery={searchQuery}
              className="border-none shadow-none"
            />
          )}
        </div>
      </CommandDialog>

      {/* Mobile Search Overlay */}
      <MobileSearchOverlay
        isOpen={mobileSearchOpen}
        onClose={() => setMobileSearchOpen(false)}
      />
    </>
  );
};

export default Navbar;
