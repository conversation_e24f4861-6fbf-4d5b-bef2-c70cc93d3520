# Email Notification System Deployment Guide

This guide explains how to deploy the email notification system for both local development and production environments.

## Overview

The email notification system uses Supabase Edge Functions to send transactional emails for:

1. **Welcome Email** - Sent when a new user registers
2. **Order Confirmation** - Sent immediately after a successful payment
3. **Delivery Confirmation** - Sent when an admin changes the order status to "delivered"

## Prerequisites

- Supabase CLI installed (`npm install -g supabase`)
- Supabase project set up
- SMTP server credentials (e.g., Gmail, SendGrid, etc.)

## Deployment Steps

### 1. Create the Email Logs Table

The email logs table is used to track sent emails and prevent duplicates. The migration script is located at:
`supabase/migrations/20240101000000_create_email_logs_table.sql`

To apply the migration:

```bash
# For production
supabase db push

# For local development
supabase migration up
```

### 2. Set Environment Variables

Set the required environment variables for the Edge Function:

```bash
# For production
supabase secrets set EMAIL_HOST=smtp.gmail.com
supabase secrets set EMAIL_PORT=587
supabase secrets set EMAIL_USERNAME=<EMAIL>
supabase secrets set EMAIL_PASSWORD=your-app-password
supabase secrets set EMAIL_FROM=<EMAIL>
supabase secrets set SITE_URL=https://thebadhees.com

# For local development
supabase secrets set --env-file .env.local EMAIL_HOST=smtp.gmail.com
supabase secrets set --env-file .env.local EMAIL_PORT=587
supabase secrets set --env-file .env.local EMAIL_USERNAME=<EMAIL>
supabase secrets set --env-file .env.local EMAIL_PASSWORD=your-app-password
supabase secrets set --env-file .env.local EMAIL_FROM=<EMAIL>
supabase secrets set --env-file .env.local SITE_URL=http://localhost:5173
```

Note: For Gmail, you'll need to create an App Password in your Google Account settings.

### 3. Deploy the Edge Function

```bash
# Navigate to the functions directory
cd supabase/functions

# For production
supabase functions deploy order-emails
supabase functions deploy welcome-email

# For local development
supabase functions serve order-emails
supabase functions serve welcome-email
```

### 4. Set Up Frontend Environment Variables

Add the following environment variables to your frontend application:

```
# .env.local or .env.production
VITE_SUPABASE_SERVICE_KEY=your-service-role-key
```

This service key is used for server-side email sending when no user session is available.

## Testing

### Local Testing

1. Start the Supabase local development server:

```bash
supabase start
```

2. Deploy the function to the local environment:

```bash
supabase functions serve order-emails
```

3. Test the functions using curl:

```bash
# Test order confirmation email
curl -X POST http://localhost:54321/functions/v1/order-emails \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"your-order-id"}'

# Test welcome email
curl -X POST http://localhost:54321/functions/v1/welcome-email \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"userId":"your-user-id"}'
```

### Production Testing

You can test the production deployment using the Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to Edge Functions
3. Test the order emails function:
   - Select the `order-emails` function
   - Click "Invoke" and provide the following JSON payload:
   ```json
   {
     "type": "order_confirmation",
     "orderId": "your-order-id"
   }
   ```

4. Test the welcome email function:
   - Select the `welcome-email` function
   - Click "Invoke" and provide the following JSON payload:
   ```json
   {
     "userId": "your-user-id"
   }
   ```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Ensure you're providing a valid JWT token in the Authorization header
   - Check that the user has permission to access the order

2. **SMTP Connection Errors**
   - Verify your SMTP credentials are correct
   - Check if your SMTP provider requires additional security settings

3. **Missing Environment Variables**
   - Ensure all required environment variables are set
   - Check for typos in variable names

4. **Template Rendering Errors**
   - Verify the email templates exist in the correct location
   - Check for syntax errors in the templates

### Viewing Logs

To view Edge Function logs:

```bash
# For production - order emails
supabase functions logs order-emails

# For production - welcome emails
supabase functions logs welcome-email

# For local development
# Logs are displayed in the terminal where you ran 'supabase functions serve'
```

## Security Considerations

- Store SMTP credentials securely as Supabase secrets
- Use Row Level Security to protect email logs
- Validate order ownership before sending emails
- Use HTTPS for all API calls
