{"name": "@types/react-helmet-async", "version": "1.0.1", "description": "TypeScript definitions for react-helmet-async", "license": "MIT", "contributors": [{"name": "forabi", "url": "https://github.com/forabi", "githubUsername": "forabi"}, {"name": "dobesv", "url": "https://github.com/dobesv", "githubUsername": "dobesv"}, {"name": "<PERSON>", "url": "https://github.com/unindented", "githubUsername": "unindented"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-helmet-async"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/react-helmet": "*"}, "typesPublisherContentHash": "ea5f61b6f639d3c0c5c76083a95485e6b9d000e341be9f10e02b066b258e02fb", "typeScriptVersion": "2.8"}