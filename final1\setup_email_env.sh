#!/bin/bash

# This script sets up the environment variables for the email functions

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo "Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Set environment variables from .env.local file
echo "Setting environment variables from .env.local file..."
supabase secrets set --env-file ./supabase/.env.local

echo "Environment variables set successfully!"
echo "You can now deploy the functions locally with:"
echo "cd supabase/functions"
echo "supabase functions serve welcome-email --no-verify-jwt"
echo "supabase functions serve order-emails --no-verify-jwt"
