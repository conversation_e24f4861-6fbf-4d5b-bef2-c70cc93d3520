import React from 'react';
import ResponsiveLayout from '@/components/layout/ResponsiveLayout';
import PageContainer from '@/components/layout/PageContainer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Truck, RefreshCw, Clock, AlertCircle } from 'lucide-react';

const ShippingReturns = () => {
  return (
    <ResponsiveLayout>
      <div className="py-12 md:py-16 lg:py-20 bg-badhees-50">
        <PageContainer>
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-6">Shipping & Returns</h1>
            <p className="text-badhees-600 mb-8">
              We want to make your shopping experience as seamless as possible. Below you'll find information about our shipping process and return policy.
            </p>

            <Tabs defaultValue="shipping" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-8">
                <TabsTrigger value="shipping">Shipping Information</TabsTrigger>
                <TabsTrigger value="returns">Returns & Exchanges</TabsTrigger>
              </TabsList>

              <TabsContent value="shipping">
                <div className="space-y-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Truck className="mr-2 h-5 w-5 text-badhees-accent" />
                        Shipping Methods & Timeframes
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-badhees-800">Standard Shipping</h3>
                        <p className="text-badhees-600">
                          5-7 business days within bangalore, 7-14 business days for other locations.
                          Shipping fee is calculated based on your location and the size of your order.
                        </p>
                      </div>

                      <div>
                        <h3 className="font-semibold text-badhees-800">Express Shipping</h3>
                        <p className="text-badhees-600">
                          2-3 business days for metro cities, 4-5 business days for other locations.
                          Additional charges apply for express shipping.
                        </p>
                      </div>

                      <div>
                        <h3 className="font-semibold text-badhees-800">Custom Furniture Delivery</h3>
                        <p className="text-badhees-600">
                          Custom furniture pieces typically require 1-3 weeks for production and delivery.
                          Our team will contact you to schedule a delivery date once your item is ready.
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Clock className="mr-2 h-5 w-5 text-badhees-accent" />
                        Order Processing
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-badhees-600">
                        Orders are typically processed within 1-2 business days. You'll receive a confirmation email once your order is placed and another notification with tracking information once it ships. For custom orders, you'll receive regular updates on the production status.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Shipping Coverage</CardTitle>
                      <CardDescription>Areas we currently serve</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-badhees-600 mb-4">
                        We currently ship to all locations within India. Shipping rates and delivery times may vary based on your location. Remote areas may require additional delivery time.
                      </p>
                      <p className="text-badhees-600">
                        International shipping is not available at this time, but we're working on expanding our services.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="returns">
                <div className="space-y-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <RefreshCw className="mr-2 h-5 w-5 text-badhees-accent" />
                        Return Policy
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-badhees-800">Standard Products</h3>
                        <p className="text-badhees-600">
                          We accept returns within 14 days of delivery for most products in their original condition and packaging. A 10% restocking fee may apply for returned items that are not defective.
                        </p>
                      </div>

                      <div>
                        <h3 className="font-semibold text-badhees-800">Custom Products</h3>
                        <p className="text-badhees-600">
                          Custom-made items cannot be returned unless there's a manufacturing defect. If you receive a custom item with a defect, please contact our customer service team within 7 days of delivery.
                        </p>
                      </div>

                      <div>
                        <h3 className="font-semibold text-badhees-800">Sale Items</h3>
                        <p className="text-badhees-600">
                          Items purchased during sales or with special discounts may have modified return policies, which will be specified at the time of purchase.
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <AlertCircle className="mr-2 h-5 w-5 text-badhees-accent" />
                        Return Process
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ol className="list-decimal list-inside space-y-2 text-badhees-600">
                        <li>Contact our customer service team to initiate a return request.</li>
                        <li>Provide your order number, the items you wish to return, and the reason for the return.</li>
                        <li>Our team will review your request and provide return instructions.</li>
                        <li>Package the item(s) securely in their original packaging if possible.</li>
                        <li>Ship the item(s) back using the provided return label or your preferred carrier.</li>
                        <li>Once we receive and inspect the returned item(s), we'll process your refund.</li>
                      </ol>

                      <p className="text-badhees-600 mt-4">
                        Refunds are typically processed within 7-10 business days after we receive the returned item(s). The refund will be issued to the original payment method used for the purchase.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Exchanges</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-badhees-600">
                        If you'd like to exchange an item for a different color, size, or model, please follow the return process and place a new order for the desired item. This ensures the fastest processing time. If the new item is of higher value, you'll need to pay the difference. If it's of lower value, we'll refund the difference.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            <div className="mt-12 bg-white p-6 rounded-lg shadow-sm border border-badhees-100">
              <h2 className="text-xl font-semibold text-badhees-800 mb-4">Need Assistance?</h2>
              <p className="text-badhees-600 mb-4">
                If you have any questions about shipping, returns, or exchanges, our customer service team is here to help.
              </p>
              <p className="text-badhees-600">
                Email: <EMAIL><br />
                Phone: 9108344363, 8197705438<br />
                Hours: Monday to Friday, 9:00 AM to 6:00 PM IST
              </p>
            </div>
          </div>
        </PageContainer>
      </div>
    </ResponsiveLayout>
  );
};

export default ShippingReturns;
