# GitHub Deployment Checklist

This checklist outlines the steps needed before deploying the project to GitHub and connecting it to Vercel.

## Environment Variables

Ensure all necessary environment variables are properly set up in your deployment environment:

### Supabase Edge Functions

- [ ] `EMAIL_HOST`: SMTP server host (e.g., smtp.gmail.com)
- [ ] `EMAIL_PORT`: SMTP server port (e.g., 587)
- [ ] `EMAIL_USERNAME`: Email account username
- [ ] `EMAIL_PASSWORD`: Email account password or app password
- [ ] `EMAIL_FROM`: Sender email address
- [ ] `SITE_URL`: Production URL (e.g., https://thebadhees.com)

### Vercel Environment Variables

- [ ] `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- [ ] `VITE_SUPABASE_SERVICE_KEY`: Your Supabase service role key (for email functions)
- [ ] `RAZORPAY_KEY_ID`: Your Razorpay key ID
- [ ] `RAZORPAY_KEY_SECRET`: Your Razorpay secret key

## Code Cleanup

- [ ] Remove any console.log statements used for debugging
- [ ] Remove any test or mock data
- [ ] Ensure all TODOs are addressed
- [ ] Check for and fix any linting errors
- [ ] Optimize images for production

## Security Checks

- [ ] Ensure no API keys or secrets are hardcoded in the source code
- [ ] Verify that all sensitive API endpoints have proper authentication
- [ ] Check that Supabase Row Level Security (RLS) policies are correctly configured
- [ ] Ensure all forms have proper validation

## Testing

- [ ] Test all critical user flows on desktop and mobile
- [ ] Test payment processing with Razorpay
- [ ] Verify that all links work correctly
- [ ] Test error handling and edge cases

### Email Testing

- [ ] Test welcome email for new user registration
- [ ] Test order confirmation email after placing an order
- [ ] Test delivery confirmation email when admin changes order status to "delivered"
- [ ] Verify all email templates render correctly on different email clients
- [ ] Check that email tracking links work correctly
- [ ] Verify that email logs are being recorded in the database
- [ ] Test email functionality with the provided test script (test-email-function.js)

## GitHub Repository Setup

- [ ] Initialize Git repository if not already done
- [ ] Create a .gitignore file to exclude:
  - [ ] node_modules
  - [ ] .env files
  - [ ] .next directory
  - [ ] Any other build artifacts or sensitive files
- [ ] Add a README.md with project description and setup instructions
- [ ] Consider adding GitHub Actions for CI/CD if needed

## Vercel Deployment

- [ ] Connect GitHub repository to Vercel
- [ ] Configure build settings:
  - [ ] Build command: `npm run build`
  - [ ] Output directory: `.next`
- [ ] Set up all environment variables in Vercel
- [ ] Configure custom domain if applicable
- [ ] Set up preview deployments for pull requests

## Post-Deployment Verification

- [ ] Verify that the site loads correctly on the production URL
- [ ] Test all critical functionality in the production environment
- [ ] Verify that Razorpay payments are processing correctly
- [ ] Monitor for any errors in logs or analytics

### Email Post-Deployment Checks

- [ ] Verify that the SITE_URL environment variable is set to the production URL
- [ ] Test welcome email flow by creating a new user account
- [ ] Test order confirmation email by placing a test order
- [ ] Test delivery confirmation email by updating an order status
- [ ] Check email logs in Supabase to confirm emails are being recorded
- [ ] Verify that email templates are rendering correctly with production URLs
- [ ] Test email functionality on different devices and email clients

## Backup and Recovery

- [ ] Create a backup of the Supabase database
- [ ] Document the recovery process
- [ ] Set up regular backups if possible

## Documentation

- [ ] Update all documentation to reflect the production environment
- [ ] Document any known issues or limitations
- [ ] Provide contact information for support
