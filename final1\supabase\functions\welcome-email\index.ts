// Welcome Email Edge Function

// Import the Supabase client creator and the Fetch API
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { SmtpClient } from 'https://deno.land/x/smtp@v0.7.0/mod.ts'
import { corsHeaders } from '../_shared/cors.ts'

// Email configuration
const EMAIL_HOST = Deno.env.get('EMAIL_HOST') || 'smtp.gmail.com'
const EMAIL_PORT = parseInt(Deno.env.get('EMAIL_PORT') || '587')
const EMAIL_USERNAME = Deno.env.get('EMAIL_USERNAME') || ''
const EMAIL_PASSWORD = Deno.env.get('EMAIL_PASSWORD') || ''
const EMAIL_FROM = Deno.env.get('EMAIL_FROM') || '<EMAIL>'
const SITE_URL = Deno.env.get('SITE_URL') || 'https://thebadhees.com'
const LOGO_URL = `${SITE_URL}/logo.png`

// Welcome email HTML template
const welcomeEmailTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to The Badhees</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eaeaea;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 20px 0;
    }
    .footer {
      text-align: center;
      padding: 20px 0;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #eaeaea;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #4a5568;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px 0;
    }
    .featured-products {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    @media only screen and (max-width: 600px) {
      .container {
        width: 100%;
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="{{logoUrl}}" alt="The Badhees" class="logo">
      <h1>Welcome to The Badhees!</h1>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Thank you for joining The Badhees family! We're excited to have you as part of our community.</p>

      <p>At The Badhees, we offer a wide range of high-quality furniture and home decor items that combine style, comfort, and durability. Whether you're looking to furnish your living room, bedroom, or any other space, we have something special for you.</p>

      <div class="featured-products">
        <h3>Explore Our Popular Categories</h3>
        <p>Discover our most loved products and find the perfect additions to your home:</p>
        <ul>
          <li>Elegant Living Room Furniture</li>
          <li>Comfortable Bedroom Collections</li>
          <li>Stylish Dining Sets</li>
          <li>Custom Interior Projects</li>
        </ul>
      </div>

      <p>Ready to start exploring? Click the button below to browse our collections:</p>

      <div style="text-align: center;">
        <a href="{{siteUrl}}/shop" class="button">Explore Our Products</a>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to contact our customer service team at <a href="mailto:<EMAIL>"><EMAIL></a> or call us at +91 XXXXXXXXXX.</p>

      <p>We look forward to helping you create a beautiful home!</p>

      <p>Warm regards,<br>The Badhees Team</p>
    </div>

    <div class="footer">
      <p>&copy; {{currentYear}} The Badhees. All rights reserved.</p>
      <p>This email was sent to {{customerEmail}}.</p>
    </div>
  </div>
</body>
</html>
`;

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize SMTP client
    let client = null;

    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '')

    // Create a Supabase client with the token
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const supabaseClient = createClient(
      supabaseUrl,
      supabaseAnonKey,
      { global: { headers: { Authorization: `Bearer ${token}` } } }
    )

    // Get the current user or the user specified in the request
    let userId;
    let userEmail;
    let userName;

    // Parse the request body
    const { userId: requestUserId } = await req.json();

    if (requestUserId) {
      // If a specific user ID is provided (for admin sending welcome emails)
      userId = requestUserId;

      // Get the user details from the database
      const { data: userData, error: userDataError } = await supabaseClient
        .from('user_profiles')
        .select('id, email, display_name')
        .eq('id', userId)
        .single();

      if (userDataError || !userData) {
        return new Response(JSON.stringify({ error: 'User not found', details: userDataError }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      userEmail = userData.email;
      userName = userData.display_name || 'Valued Customer';
    } else {
      // If no specific user ID is provided, use the authenticated user
      const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token);
      if (userError || !user) {
        return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      userId = user.id;

      // Get the user profile details
      const { data: profileData } = await supabaseClient
        .from('user_profiles')
        .select('display_name, email')
        .eq('id', userId)
        .single();

      userEmail = user.email;
      userName = profileData?.display_name || user.email.split('@')[0];
    }

    // Check if we've already sent a welcome email to this user
    const { data: emailLog, error: logError } = await supabaseClient
      .from('email_logs')
      .select('id')
      .eq('email_type', 'welcome_email')
      .eq('recipient', userEmail)
      .eq('status', 'sent')
      .maybeSingle();

    if (emailLog) {
      console.log(`Welcome email already sent to ${userEmail}`);
      return new Response(JSON.stringify({
        success: true,
        message: 'Welcome email already sent to this user'
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if SMTP credentials are available
    if (!EMAIL_USERNAME || !EMAIL_PASSWORD) {
      console.error('Missing SMTP credentials');
      return new Response(JSON.stringify({ error: 'Email service not configured' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Initialize SMTP client
    client = new SmtpClient();

    // Connect to the SMTP server
    try {
      await client.connectTLS({
        hostname: EMAIL_HOST,
        port: EMAIL_PORT,
        username: EMAIL_USERNAME,
        password: EMAIL_PASSWORD,
      });
    } catch (smtpError) {
      console.error('SMTP connection error:', smtpError);
      return new Response(JSON.stringify({ error: 'Failed to connect to email server', details: smtpError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Prepare email content
    const currentYear = new Date().getFullYear();
    const emailHtml = welcomeEmailTemplate
      .replace(/{{customerName}}/g, userName)
      .replace(/{{customerEmail}}/g, userEmail)
      .replace(/{{logoUrl}}/g, LOGO_URL)
      .replace(/{{siteUrl}}/g, SITE_URL)
      .replace(/{{currentYear}}/g, currentYear.toString());

    const emailSubject = `Welcome to The Badhees - Your Journey to Beautiful Furniture Begins!`;

    // Send the email
    let emailSent = false;
    try {
      await client.send({
        from: EMAIL_FROM,
        to: userEmail,
        subject: emailSubject,
        html: emailHtml,
      });

      emailSent = true;
      console.log(`Welcome email sent successfully to ${userEmail}`);
    } catch (sendError) {
      console.error('Error sending welcome email:', sendError);
      return new Response(JSON.stringify({ error: 'Failed to send email', details: sendError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } finally {
      // Always close the SMTP connection
      if (client) {
        try {
          await client.close();
        } catch (closeError) {
          console.error('Error closing SMTP connection:', closeError);
        }
      }
    }

    // Record the email in the database
    if (emailSent) {
      try {
        const { error: logError } = await supabaseClient
          .from('email_logs')
          .insert({
            order_id: null, // No order associated with welcome email
            email_type: 'welcome_email',
            recipient: userEmail,
            status: 'sent',
            sent_at: new Date().toISOString()
          });

        if (logError) {
          console.error('Error logging welcome email:', logError);
          // Continue even if logging fails
        }
      } catch (logError) {
        console.error('Exception logging welcome email:', logError);
        // Continue even if logging fails
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Welcome email sent successfully'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error in welcome email function:', error);
    return new Response(JSON.stringify({ error: error.message || 'Unknown error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
})
