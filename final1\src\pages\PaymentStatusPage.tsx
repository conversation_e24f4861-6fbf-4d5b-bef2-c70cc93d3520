/**
 * Payment Status Page
 *
 * This page displays the status of a payment and allows users to retry failed payments.
 */
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Container } from '../components/ui/container';
import { PageHeader } from '../components/ui/PageHeader';
import PaymentRecovery from '../components/payment/PaymentRecovery';
import { AuthGuard } from '../components/auth/AuthGuard';

const PaymentStatusPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();

  if (!orderId) {
    navigate('/orders');
    return null;
  }

  return (
    <AuthGuard>
      <Helmet>
        <title>Payment Status | The Badhees</title>
      </Helmet>
      <Container>
        <PageHeader
          title="Payment Status"
          description="Check the status of your payment and retry if needed"
          className="mb-8"
        />

        <div className="max-w-3xl mx-auto mb-12">
          <PaymentRecovery
            orderId={orderId}
            onSuccess={() => navigate(`/orders/${orderId}`)}
            onCancel={() => navigate('/orders')}
          />
        </div>
      </Container>
    </AuthGuard>
  );
};

export default PaymentStatusPage;
