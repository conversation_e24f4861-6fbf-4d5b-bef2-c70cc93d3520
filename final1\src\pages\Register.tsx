
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Eye, EyeOff } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import GoogleLoginButton from '@/components/auth/GoogleLoginButton';
import '@/styles/auth-forms.css';

const Register = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { register, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // If already authenticated, redirect to home
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const validatePassword = () => {
    if (password !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return false;
    }

    setPasswordError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePassword()) {
      return;
    }

    setIsLoading(true);

    try {
      const success = await register(name, email, password);
      if (success) {
        navigate('/');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-28 pb-16">
        <div className="max-w-md mx-auto px-4 sm:px-8">
          <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-8">
            <h1 className="text-2xl font-bold text-badhees-800 mb-6 text-center">Create an Account</h1>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-badhees-700 mb-1">
                  Full Name
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="John Doe"
                  required
                  className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-badhees-700 mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-badhees-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Create a password"
                    required
                    className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-badhees-500"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                <p className="text-xs text-badhees-500 mt-1">Must be at least 6 characters long</p>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-badhees-700 mb-1">
                  Confirm Password
                </label>
                <input
                  id="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  required
                  className={`w-full px-4 py-3 rounded-md border ${
                    passwordError ? 'border-red-500' : 'border-badhees-200'
                  } focus:outline-none focus:ring-1 focus:ring-badhees-accent`}
                />
                {passwordError && (
                  <p className="text-xs text-red-500 mt-1">{passwordError}</p>
                )}
              </div>

              <div className="auth-checkbox">
                <input
                  id="terms"
                  type="checkbox"
                  required
                />
                <label htmlFor="terms">
                  I agree to the{' '}
                  <a href="#" className="text-badhees-accent hover:text-badhees-700">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="#" className="text-badhees-accent hover:text-badhees-700">
                    Privacy Policy
                  </a>
                </label>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors flex items-center justify-center"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </button>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>

              <GoogleLoginButton fullWidth />
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-badhees-600">
                Already have an account?{' '}
                <Link to="/login" className="font-medium text-badhees-accent hover:text-badhees-700">
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Register;
