-- Update the handle_new_user function to trigger welcome email
CREATE OR <PERSON><PERSON>LACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
DECLARE
  v_success BOOLEAN;
BEGIN
  -- Insert the user profile as before
  INSERT INTO public.user_profiles (
    id, 
    display_name, 
    email, 
    role,
    created_at,
    updated_at
  )
  VALUES (
    new.id, 
    COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
    new.email,
    'user',  -- Default role is always 'user'
    now(),
    now()
  );
  
  -- Log the welcome email in email_logs to track it
  -- This doesn't actually send the email, just records that we should send it
  INSERT INTO email_logs (
    order_id,
    email_type,
    recipient,
    status,
    sent_at
  ) VALUES (
    NULL, -- No order associated with welcome email
    'welcome_email',
    new.email,
    'pending', -- Mark as pending until it's actually sent
    NULL -- Will be updated when email is sent
  );
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create a function to process pending welcome emails
CREATE OR REPLACE FUNCTION process_pending_welcome_emails()
RETURNS void AS $$
DECLARE
  v_email_log RECORD;
  v_user RECORD;
BEGIN
  -- Get all pending welcome emails
  FOR v_email_log IN 
    SELECT * FROM email_logs 
    WHERE email_type = 'welcome_email' 
    AND status = 'pending'
    LIMIT 10 -- Process in batches
  LOOP
    -- Get the user information
    SELECT * INTO v_user 
    FROM auth.users 
    WHERE email = v_email_log.recipient;
    
    IF FOUND THEN
      -- Update the email log status to processing
      UPDATE email_logs
      SET status = 'processing'
      WHERE id = v_email_log.id;
      
      -- In a real implementation, you would call the Edge Function here
      -- For now, we'll just mark it as sent
      UPDATE email_logs
      SET status = 'sent',
          sent_at = NOW()
      WHERE id = v_email_log.id;
    ELSE
      -- If user not found, mark as failed
      UPDATE email_logs
      SET status = 'failed',
          sent_at = NOW()
      WHERE id = v_email_log.id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to call the welcome email Edge Function
CREATE OR REPLACE FUNCTION http_post_welcome_email(user_id UUID)
RETURNS text AS $$
DECLARE
  v_url text := rtrim(current_setting('app.settings.supabase_url', true), '/') || '/functions/v1/welcome-email';
  v_headers jsonb := jsonb_build_object(
    'Content-Type', 'application/json',
    'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true)
  );
  v_body jsonb := jsonb_build_object('userId', user_id);
  v_response json;
BEGIN
  SELECT content::json INTO v_response
  FROM http((
    'POST',
    v_url,
    v_headers,
    v_body::text,
    10 -- timeout in seconds
  )::http_request);
  
  RETURN v_response::text;
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object('error', SQLERRM)::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
