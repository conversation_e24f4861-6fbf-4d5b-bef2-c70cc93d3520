# Email Implementation Guide

This guide provides instructions for implementing email functionality in the application. The current codebase includes placeholder email functions that log messages but don't actually send emails.

## Current Status

The application has been cleaned up to remove:
- All email-related Edge Functions
- Email templates
- Email database tables and triggers
- Non-functional email code

The `emailService.ts` file now contains placeholder functions that log messages but don't send actual emails.

## Implementation Options

You have several options for implementing email functionality:

### 1. Direct SMTP Integration

Use a library like `nodemailer` to send emails directly via SMTP.

**Setup:**
1. Install the required packages:
   ```bash
   npm install nodemailer
   ```

2. Configure SMTP settings in your `.env` file:
   ```
   EMAIL_HOST=smtp.example.com
   EMAIL_PORT=587
   EMAIL_USERNAME=your_username
   EMAIL_PASSWORD=your_password
   EMAIL_FROM=<EMAIL>
   ```

3. Update the `emailService.ts` file to use nodemailer.

### 2. Email Service Providers

Use a service like SendGrid, Mailgun, or Amazon SES.

**SendGrid Example:**
1. Install the required packages:
   ```bash
   npm install @sendgrid/mail
   ```

2. Configure SendGrid in your `.env` file:
   ```
   SENDGRID_API_KEY=your_api_key
   EMAIL_FROM=<EMAIL>
   ```

3. Update the `emailService.ts` file to use SendGrid.

### 3. Supabase Edge Functions (Advanced)

If you want to continue using Supabase Edge Functions:

1. Create a new Edge Function for email sending
2. Configure the function with your email service credentials
3. Deploy the function to Supabase
4. Update the `emailService.ts` file to call the Edge Function

## Implementation Steps

### Basic Implementation with SendGrid

Here's a simple implementation using SendGrid:

1. Sign up for a SendGrid account and get an API key

2. Install the SendGrid package:
   ```bash
   npm install @sendgrid/mail
   ```

3. Update your `.env` file:
   ```
   SENDGRID_API_KEY=your_api_key
   EMAIL_FROM=<EMAIL>
   ```

4. Update the `emailService.ts` file:

```typescript
import { supabase } from '@/lib/supabase';
import sgMail from '@sendgrid/mail';

// Initialize SendGrid
sgMail.setApiKey(import.meta.env.VITE_SENDGRID_API_KEY);

// Email template types
export enum EmailTemplate {
  WELCOME = 'welcome_email',
  ORDER_CONFIRMATION = 'order_confirmation',
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  ORDER_SHIPPED = 'order_shipped',
  ORDER_DELIVERED = 'delivery_confirmation',
  ORDER_CANCELLED = 'order_cancelled',
}

// Email data interface
export interface EmailData {
  to: string;
  subject?: string;
  templateId: EmailTemplate;
  data: Record<string, any>;
}

// Send an email using SendGrid
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Create email content based on template
    let htmlContent = '';
    let subject = emailData.subject || 'Notification from The Badhees';
    
    // In a real implementation, you would load HTML templates
    // and replace placeholders with actual data
    switch (emailData.templateId) {
      case EmailTemplate.WELCOME:
        htmlContent = `<h1>Welcome to The Badhees!</h1>
                      <p>Hello ${emailData.data.customer_name || 'Valued Customer'},</p>
                      <p>Thank you for joining The Badhees family!</p>`;
        break;
      case EmailTemplate.ORDER_CONFIRMATION:
        htmlContent = `<h1>Order Confirmation</h1>
                      <p>Hello ${emailData.data.customer_name || 'Valued Customer'},</p>
                      <p>Your order #${emailData.data.order?.order_number} has been confirmed.</p>`;
        break;
      // Add other templates as needed
      default:
        htmlContent = `<h1>Notification</h1>
                      <p>Hello ${emailData.data.customer_name || 'Valued Customer'},</p>
                      <p>This is a notification from The Badhees.</p>`;
    }
    
    // Send the email
    await sgMail.send({
      to: emailData.to,
      from: import.meta.env.VITE_EMAIL_FROM || '<EMAIL>',
      subject: subject,
      html: htmlContent,
    });
    
    console.log(`Email sent to ${emailData.to}`);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
};

// The rest of your email functions can remain the same,
// but update them to use the new sendEmail function
```

5. Create HTML templates for your emails in a new `src/email-templates` directory.

## Email Templates

Create HTML templates for different types of emails:

1. Welcome Email (`welcome.html`)
2. Order Confirmation (`order-confirmation.html`)
3. Payment Success (`payment-success.html`)
4. Order Status Updates (`order-status.html`)

## Testing Your Implementation

1. Create a test script to verify your email implementation:

```typescript
// test-email.js
import { sendWelcomeEmail } from './src/services/emailService';

// Test sending a welcome email
sendWelcomeEmail('test-user-id', '<EMAIL>')
  .then(result => {
    console.log('Email sent:', result);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

2. Run the test script:
```bash
node test-email.js
```

## Conclusion

This guide provides a starting point for implementing email functionality in your application. Choose the approach that best fits your needs and budget.

Remember to:
- Keep sensitive email credentials in your `.env` file and never commit them to version control
- Create professional-looking email templates
- Test thoroughly before deploying to production
- Consider rate limits and quotas of your chosen email service

For more advanced implementations, consider using a template engine like Handlebars or EJS to generate your email HTML content dynamically.
