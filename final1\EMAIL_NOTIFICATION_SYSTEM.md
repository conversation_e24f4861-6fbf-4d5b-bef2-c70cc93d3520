# Email Notification System

This document explains how to set up and use the email notification system for order confirmations and delivery updates.

## Overview

The email notification system uses Supabase Edge Functions to send transactional emails for:

1. **Order Confirmation** - Sent immediately after a successful payment
2. **Delivery Confirmation** - Sent when an admin changes the order status to "delivered"

The system includes:

- HTML email templates with responsive design
- Edge Functions for email delivery
- Database logging to prevent duplicate emails
- Fallback mechanisms for reliability

## Setup Instructions

### 1. Create the Email Logs Table

Run the SQL script to create the email logs table:

```sql
-- Run this in the Supabase SQL Editor
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  email_type TEXT NOT NULL,
  recipient TEXT NOT NULL,
  status TEXT NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_email_logs_order_id ON email_logs(order_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);

-- Set up Row Level Security (RLS)
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_logs
-- Admins can view all email logs
CREATE POLICY "Admins can view all email logs"
  ON email_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Users can view their own email logs
CREATE POLICY "Users can view their own email logs"
  ON email_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM orders
      WHERE orders.id = email_logs.order_id
      AND orders.user_id = auth.uid()
    )
  );

-- Only the system can insert email logs
CREATE POLICY "System can insert email logs"
  ON email_logs FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL);
```

### 2. Deploy the Edge Function

1. Navigate to the Supabase Edge Functions directory:
   ```
   cd final1/supabase/functions
   ```

2. Deploy the Edge Function using the Supabase CLI:
   ```
   supabase functions deploy order-emails
   ```

3. Set the required environment variables:
   ```
   supabase secrets set EMAIL_HOST=smtp.gmail.com
   supabase secrets set EMAIL_PORT=587
   supabase secrets set EMAIL_USERNAME=<EMAIL>
   supabase secrets set EMAIL_PASSWORD=your-app-password
   supabase secrets set EMAIL_FROM=<EMAIL>
   supabase secrets set SITE_URL=https://thebadhees.com
   ```

   Note: For Gmail, you'll need to create an App Password in your Google Account settings.

### 3. Test the Email System

You can test the email system using the Supabase CLI:

```
supabase functions invoke order-emails --body '{"type":"order_confirmation","orderId":"your-order-id"}'
```

Replace `your-order-id` with an actual order ID from your database.

## Usage

### Sending Order Confirmation Emails

To send an order confirmation email after a successful payment:

```typescript
import { sendOrderConfirmationViaEdgeFunction } from '@/services/emailService';

// After payment is successful
const paymentSuccessHandler = async (orderId: string) => {
  // Process payment...
  
  // Send confirmation email
  await sendOrderConfirmationViaEdgeFunction(orderId);
};
```

### Sending Delivery Confirmation Emails

To send a delivery confirmation email when an admin updates the order status:

```typescript
import { sendOrderStatusUpdateEmail } from '@/services/emailService';

// In the admin dashboard when updating order status
const updateOrderStatus = async (orderId: string, status: string, userEmail: string) => {
  // Update order status in database...
  
  // Send status update email
  if (status === 'delivered') {
    await sendOrderStatusUpdateEmail(orderId, userEmail, status);
  }
};
```

## Email Templates

The email templates are located in:
- `supabase/functions/order-emails/templates/order-confirmation.html`
- `supabase/functions/order-emails/templates/delivery-confirmation.html`

You can customize these templates to match your brand's style and messaging.

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check the Supabase Edge Function logs
   - Verify SMTP credentials are correct
   - Ensure the order exists in the database

2. **Duplicate emails**
   - The system checks the `email_logs` table to prevent duplicates
   - If you need to resend an email, delete the corresponding entry from `email_logs`

3. **Edge Function errors**
   - The system includes fallback mechanisms to use the existing email queue
   - Check the Supabase logs for detailed error messages

### Viewing Email Logs

You can view email logs in the Supabase Table Editor or with this query:

```sql
SELECT 
  email_logs.id,
  email_logs.email_type,
  email_logs.recipient,
  email_logs.status,
  email_logs.sent_at,
  orders.id as order_id
FROM email_logs
JOIN orders ON email_logs.order_id = orders.id
ORDER BY email_logs.sent_at DESC;
```

## Security Considerations

- The Edge Function requires authentication to prevent unauthorized use
- Email templates do not contain sensitive information
- SMTP credentials are stored as Supabase secrets, not in the codebase
- Row Level Security ensures users can only see their own email logs
