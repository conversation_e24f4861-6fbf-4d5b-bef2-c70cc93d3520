# Welcome Email System Setup Guide

This guide explains how to set up the welcome email system for The Badhees e-commerce website. The system sends a welcome email to new users when they sign up.

## Overview

The welcome email system consists of:

1. A Supabase Edge Function that sends the welcome email
2. A database trigger that logs pending welcome emails
3. Client-side code that triggers the welcome email when a user registers

## 1. Deploy the Edge Function

### Local Development

1. Navigate to the Supabase Edge Functions directory:
   ```bash
   cd final1/supabase/functions
   ```

2. Deploy the Edge Function using the Supabase CLI:
   ```bash
   supabase functions deploy welcome-email
   ```

### Production Deployment

When deploying to production, the Edge Function will be automatically deployed with your Supabase project.

## 2. Set Environment Variables

Set the required environment variables for the Edge Function:

```bash
# For local development
supabase secrets set --env-file .env.local

# For production
supabase secrets set EMAIL_HOST=smtp.gmail.com
supabase secrets set EMAIL_PORT=587
supabase secrets set EMAIL_USERNAME=<EMAIL>
supabase secrets set EMAIL_PASSWORD=your-app-password
supabase secrets set EMAIL_FROM=<EMAIL>
supabase secrets set SITE_URL=https://thebadhees.com
```

## 3. Apply Database Migrations

Run the SQL migration to set up the welcome email trigger:

```bash
# For local development
supabase migration up

# For production
supabase db push
```

## 4. Testing the Welcome Email

You can test the welcome email system by:

1. Registering a new user through the website
2. Manually triggering the welcome email for an existing user:

```javascript
// In the browser console (when logged in)
const { data, error } = await supabase.functions.invoke('welcome-email', {
  body: { userId: 'your-user-id' }
});
console.log(data, error);
```

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check the Supabase Edge Function logs
   - Verify SMTP credentials are correct
   - Ensure the user exists in the database

2. **Duplicate emails**
   - The system checks the `email_logs` table to prevent duplicates
   - If you need to resend a welcome email, delete the corresponding entry from `email_logs`

### Viewing Email Logs

You can view email logs in the Supabase Table Editor or with this query:

```sql
SELECT 
  email_logs.id,
  email_logs.email_type,
  email_logs.recipient,
  email_logs.status,
  email_logs.sent_at
FROM email_logs
WHERE email_type = 'welcome_email'
ORDER BY email_logs.sent_at DESC;
```

## Security Considerations

- The Edge Function requires authentication to prevent unauthorized use
- Email templates do not contain sensitive information
- SMTP credentials are stored as Supabase secrets, not in the codebase
- Row Level Security ensures users can only see their own email logs

## Customizing the Email Template

The welcome email template is located in:
- `supabase/functions/welcome-email/templates/welcome.html`

You can customize this template to match your brand's style and messaging.

## Email Queue System

For reliability, the system uses both direct email sending via the Edge Function and a fallback email queue system:

1. When a user registers, the client attempts to send the welcome email via the Edge Function
2. If that fails, it falls back to the email queue system
3. A database trigger also logs pending welcome emails that can be processed by a scheduled job

This ensures that welcome emails are sent even if there are temporary issues with the Edge Function.
