# Quick Email Verification Setup Guide

## 🚀 Quick Setup Checklist

### ✅ Step 1: Supabase Dashboard Configuration

1. **Go to your Supabase project dashboard**
2. **Navigate to Authentication > URL Configuration**
3. **Set Site URL:**
   - Production: `https://your-domain.com`
   - Local: `http://localhost:5173`

4. **Add Redirect URLs:**
   ```
   https://your-domain.com/confirm-email
   http://localhost:5173/confirm-email
   ```

### ✅ Step 2: Email Template Setup

1. **Go to Authentication > Email Templates**
2. **Click on "Confirm signup"**
3. **Copy the HTML from:** `email-templates/supabase-email-template.html`
4. **Paste into the "Message (HTML)" field**
5. **Set Redirect URL to:** `{{ .SiteURL }}/confirm-email?token={{ .TokenHash }}&type=signup`
6. **Click Save**

### ✅ Step 3: Test the Flow

1. **Deploy your code changes**
2. **Register with a new email address**
3. **Check your email**
4. **Click the confirmation link**
5. **Verify you see the custom confirmation page**

## 🔧 What Each File Does

- `EmailConfirmation.tsx` - Custom confirmation page that users see
- `supabase-email-template.html` - The email template for Supabase
- `email-confirmation.html` - Alternative template (not used in Supabase)

## 🎯 Expected User Flow

1. User registers → Gets branded email from "The Badhees"
2. User clicks "Confirm Your Email Address" → Goes to your custom page
3. Page shows "Email confirmed successfully!" → User clicks "Continue to Sign In"
4. User is redirected to login page with success message

## 🚨 Common Issues & Solutions

### Issue: "localhost refused to connect"
**Solution:** Make sure Site URL in Supabase is set to your actual domain, not localhost (for production).

### Issue: Generic Supabase email
**Solution:** Make sure you copied the HTML template correctly and saved it in Supabase.

### Issue: Confirmation link doesn't work
**Solution:** Check that the redirect URL format is exactly: `{{ .SiteURL }}/confirm-email?token={{ .TokenHash }}&type=signup`

### Issue: Page shows error
**Solution:** Check browser console for errors and verify the `/confirm-email` route exists in your app.

## 📝 Template Variables Reference

In your Supabase email template, you can use:
- `{{ .ConfirmationURL }}` - Complete confirmation link
- `{{ .SiteURL }}` - Your website URL
- `{{ .TokenHash }}` - Verification token
- `{{ .Email }}` - User's email address

## 🔒 Security Notes

- Email confirmation tokens expire after 24 hours
- Links can only be used once
- Always use HTTPS in production
- Only add trusted domains to redirect URLs

## 📞 Need Help?

If you're still having issues:
1. Check the browser console for JavaScript errors
2. Verify all URLs are correctly configured
3. Test with different email providers
4. Check Supabase logs for any errors
