# Email Function Deployment Guide

This guide provides step-by-step instructions for deploying the unified email service function to Supabase.

## Quick Start

To deploy the email function to Supabase without Docker:

1. Navigate to the project root:
```bash
cd final1
```

2. Deploy the function using the `--use-docker=false` flag:
```bash
npx supabase functions deploy email-service --use-docker=false
```

3. Set the environment variables:
```bash
npx supabase secrets set EMAIL_HOST=smtp.gmail.com EMAIL_PORT=587 EMAIL_USERNAME=<EMAIL> EMAIL_PASSWORD="psmr krvj ccdv vphq" EMAIL_FROM=<EMAIL> SITE_URL=https://thebadhees.com
```

4. Verify deployment:
```bash
npx supabase functions list
```

This will show you all deployed functions and their status.

## Troubleshooting the Docker Error

If you see the error "Docker Desktop is required", use the `--use-docker=false` flag as shown above. This allows you to deploy the function without having Docker installed.

The error message:
```
failed to inspect docker image: error during connect: in the default daemon configuration on Windows, the docker client must be run with elevated privileges to connect: Get "http://%2F%2F.%2Fpipe%2Fdocker_engine/v1.49/images/public.ecr.aws/supabase/edge-runtime:v1.67.4/json": open //./pipe/docker_engine: The system cannot find the file specified.
```

This is normal when you don't have Docker installed, but it doesn't affect deployment to Supabase's servers.

## Testing the Deployed Function

You can test the function using curl or Postman:

```bash
# Test welcome email
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/email-service \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/email-service \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace:
- `YOUR_PROJECT_ID` with your Supabase project ID
- `YOUR_ANON_KEY` with your Supabase anon key
- `YOUR_USER_ID` with a valid user ID from your database
- `YOUR_ORDER_ID` with a valid order ID from your database

## Project Cleanup

The following files have been removed as they are no longer needed:

1. `final1/supabase/functions/order-emails` - Replaced by the unified email-service
2. `final1/supabase/functions/welcome-email` - Replaced by the unified email-service
3. `final1/EMAIL_SETUP_GUIDE.md` - Outdated guide
4. `final1/EMAIL_NOTIFICATION_DEPLOYMENT.md` - Outdated guide
5. `final1/WELCOME_EMAIL_SETUP.md` - Outdated guide
6. `final1/SIMPLIFIED_EMAIL_DEPLOYMENT.md` - Outdated guide

The unified email-service function now handles all email types:
- Welcome emails
- Order confirmation emails
- Delivery confirmation emails

## Additional Resources

For more detailed information, see:
- `final1/EMAIL_FUNCTION_DEPLOYMENT_GUIDE.md` - Comprehensive deployment guide
- `final1/supabase/functions/README.md` - Overview of the email function
