
import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Mail, Phone, MapPin, Clock, Send, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { submitContactForm } from '@/services/contactService';
import { useAuth } from '@/context/SupabaseAuthContext';

const Contact = () => {
  const { user, isAuthenticated } = useAuth();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Auto-fill form fields if user is logged in
    if (isAuthenticated && user) {
      setName(user.name || '');
      setEmail(user.email || '');
      // setPhone(user.phone || ''); // Removed because 'phone' does not exist on type 'User'
    }
  }, [isAuthenticated, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !email.trim() || !subject.trim() || !message.trim()) {
      toast({
        title: 'Missing information',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      let success = false;

      // Create submission object
      const submission = {
        full_name: name,
        email,
        phone: phone || undefined,
        subject,
        message,
        user_id: isAuthenticated ? user?.id : undefined
      };

      // Submit the contact form
      const result = await submitContactForm(submission);
      success = !!result;

      if (success) {
        // Reset form fields that aren't auto-filled
        setSubject('');
        setMessage('');

        // Only reset personal info if not logged in
        if (!isAuthenticated) {
          setName('');
          setEmail('');
          setPhone('');
        }
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast({
        title: 'Error',
        description: (
          <div>
            Failed to submit your message. Please try again or use our <a
              href="/contact-fallback.html"
              target="_blank"
              className="underline font-medium"
            >
              alternative contact form
            </a>.
          </div>
        ),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-28 pb-16">
        {/* Hero Section */}
        <div className="bg-badhees-50 py-16">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-6">
                Get In Touch
              </h1>
              <p className="text-lg text-badhees-600">
                Have questions, feedback, or special requests? We'd love to hear from you.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information & Form */}
        <div className="py-16">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Contact Information */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-sm border border-badhees-100 p-8 h-full">
                  <h2 className="text-xl font-bold text-badhees-800 mb-6">Contact Information</h2>

                  <div className="space-y-6">
                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-10 h-10 rounded-full bg-badhees-50 flex items-center justify-center">
                          <Mail className="h-5 w-5 text-badhees-accent" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-badhees-800 mb-1">Email</h3>
                        <a href="mailto:<EMAIL>" className="text-badhees-600 hover:text-badhees-accent">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-10 h-10 rounded-full bg-badhees-50 flex items-center justify-center">
                          <Phone className="h-5 w-5 text-badhees-accent" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-badhees-800 mb-1">Phone</h3>
                        <div className="flex flex-col">
                          <a href="tel:9108344363" className="text-badhees-600 hover:text-badhees-accent">
                            9108344363
                          </a>
                          <a href="tel:8197705438" className="text-badhees-600 hover:text-badhees-accent mt-1">
                            8197705438
                          </a>
                        </div>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-10 h-10 rounded-full bg-badhees-50 flex items-center justify-center">
                          <MapPin className="h-5 w-5 text-badhees-accent" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-badhees-800 mb-1">Address</h3>
                        <p className="text-badhees-600">
                          The Badhees<br />
                          Opposite Vasudha layout<br />
                          Appajipura road, Koraluru<br />
                          Bangalore 560067
                        </p>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-10 h-10 rounded-full bg-badhees-50 flex items-center justify-center">
                          <Clock className="h-5 w-5 text-badhees-accent" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-badhees-800 mb-1">Hours</h3>
                        <p className="text-badhees-600">
                          Monday - Friday: 9am - 6pm<br />
                          Saturday: 10am - 4pm<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-sm border border-badhees-100 p-8">
                  <h2 className="text-xl font-bold text-badhees-800 mb-6">Send Us a Message</h2>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-badhees-700 mb-1">
                          Full Name
                        </label>
                        <input
                          id="name"
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Your name"
                          required
                          className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                          disabled={isAuthenticated}
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-badhees-700 mb-1">
                          Email
                        </label>
                        <input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Your email"
                          required
                          className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                          disabled={isAuthenticated}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-badhees-700 mb-1">
                        Phone Number
                      </label>
                      <input
                        id="phone"
                        type="tel"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder="Your phone number (optional)"
                        className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        disabled={isAuthenticated}
                      />
                    </div>

                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-badhees-700 mb-1">
                        Subject
                      </label>
                      <input
                        id="subject"
                        type="text"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder="What is this regarding?"
                        required
                        className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                      />
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-badhees-700 mb-1">
                        Message
                      </label>
                      <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="How can we help you?"
                        required
                        rows={6}
                        className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent resize-none"
                      ></textarea>
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-badhees-800 text-white px-6 py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          Send Message
                          <Send className="ml-2 h-4 w-4" />
                        </>
                      )}
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Map Section */}
        <div className="py-12">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="bg-badhees-50 rounded-xl overflow-hidden h-[400px] flex items-center justify-center">
              <div className="text-center p-8">
                <h3 className="text-xl font-bold text-badhees-800 mb-4">Visit Our Showroom</h3>
                <p className="text-badhees-600 mb-6">
                  Experience our furniture in person at our showroom in Bangalore.
                </p>
                <a
                  href="https://maps.app.goo.gl/Eo9Eo9Eo9Eo9Eo9E9"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-badhees-800 text-white px-6 py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors inline-flex items-center"
                >
                  <MapPin className="mr-2 h-4 w-4" />
                  Get Directions
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Contact;
