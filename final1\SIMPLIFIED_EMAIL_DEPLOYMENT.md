# Simplified Email Functions Deployment Guide

This guide provides a simplified approach to deploy the email functions to your production Supabase instance.

## Prerequisites

- Supabase account with access to your project
- Supabase CLI installed (we'll use `npx` to run it)
- Your email configuration (already in `supabase/.env.local`)

## Step 1: Login to Supabase

Open a command prompt and run:

```
npx supabase login
```

This will open a browser window where you can authenticate with Supabase.

## Step 2: Link Your Project

Run the following command to link your local project to your Supabase project:

```
npx supabase link --project-ref YOUR_PROJECT_ID
```

Replace `YOUR_PROJECT_ID` with your Supabase project ID. You can find this in the URL of your Supabase dashboard: `https://app.supabase.com/project/YOUR_PROJECT_ID`

## Step 3: Set Environment Variables

Set the email environment variables in your Supabase project:

```
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD="psmr krvj ccdv vphq"
npx supabase secrets set EMAIL_FROM=<EMAIL>
npx supabase secrets set SITE_URL=https://thebadhees.com
```

Note: Replace `https://thebadhees.com` with your actual production URL.

## Step 4: Deploy the Functions

Deploy the welcome email function:

```
cd supabase/functions
npx supabase functions deploy welcome-email
```

Deploy the order emails function:

```
npx supabase functions deploy order-emails
```

## Step 5: Verify Deployment

Check the logs to verify the functions were deployed successfully:

```
npx supabase functions logs welcome-email
npx supabase functions logs order-emails
```

## Step 6: Test the Functions

You can test the functions using the Supabase dashboard or by making HTTP requests to your functions:

```
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/welcome-email \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"userId":"USER_ID"}'
```

Replace:
- `YOUR_PROJECT_ID` with your Supabase project ID
- `YOUR_ANON_KEY` with your Supabase anon key
- `USER_ID` with a valid user ID from your database

## Troubleshooting

### Common Issues

1. **Authentication errors**
   - Make sure you're logged in to Supabase: `npx supabase login`
   - Verify your project is linked: `npx supabase link --project-ref YOUR_PROJECT_ID`

2. **Deployment errors**
   - Check the function logs: `npx supabase functions logs welcome-email`
   - Verify your environment variables are set correctly

3. **Email sending errors**
   - Verify your SMTP credentials are correct
   - Check if your email provider is blocking the connection
   - Gmail limits sending to 500 emails per day

## Next Steps

Once your functions are deployed, you can integrate them with your frontend application. Here's how to call them:

```javascript
// Example of calling the welcome email function
async function sendWelcomeEmail(userId) {
  const { data, error } = await supabase.functions.invoke('welcome-email', {
    body: { userId }
  });
  
  if (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
  
  return data.success;
}

// Example of calling the order emails function
async function sendOrderEmail(type, orderId) {
  const { data, error } = await supabase.functions.invoke('order-emails', {
    body: { type, orderId }
  });
  
  if (error) {
    console.error('Error sending order email:', error);
    return false;
  }
  
  return data.success;
}
```

## Security Considerations

1. **Never commit email credentials to your repository**
2. **Use environment variables** for all sensitive information
3. **Implement rate limiting** to prevent abuse
4. **Validate email recipients** before sending
5. **Use Row Level Security** to protect email logs
6. **Sanitize all user input** used in email templates
