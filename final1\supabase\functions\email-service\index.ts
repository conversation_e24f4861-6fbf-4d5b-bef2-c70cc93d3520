// Email Service Edge Function
// This function handles all email sending operations including welcome emails and order notifications

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts"
import { corsHeaders } from "../_shared/cors.ts"

// Email configuration
const EMAIL_HOST = Deno.env.get('EMAIL_HOST') || 'smtp.gmail.com'
const EMAIL_PORT = parseInt(Deno.env.get('EMAIL_PORT') || '587')
const EMAIL_USERNAME = Deno.env.get('EMAIL_USERNAME') || ''
const EMAIL_PASSWORD = Deno.env.get('EMAIL_PASSWORD') || ''
const EMAIL_FROM = Deno.env.get('EMAIL_FROM') || '<EMAIL>'
const SITE_URL = Deno.env.get('SITE_URL') || 'https://thebadhees.com'
const LOGO_URL = `${SITE_URL}/logo.png`

// Welcome email HTML template
const welcomeEmailTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to The Badhees</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eaeaea;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 20px 0;
    }
    .footer {
      text-align: center;
      padding: 20px 0;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #eaeaea;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #4a5568;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px 0;
    }
    .featured-products {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    @media only screen and (max-width: 600px) {
      .container {
        width: 100%;
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="{{logoUrl}}" alt="The Badhees" class="logo">
      <h1>Welcome to The Badhees!</h1>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Thank you for joining The Badhees family! We're excited to have you as part of our community.</p>

      <p>At The Badhees, we offer a wide range of high-quality furniture and home decor items that combine style, comfort, and durability. Whether you're looking to furnish your living room, bedroom, or any other space, we have something special for you.</p>

      <div class="featured-products">
        <h3>Explore Our Popular Categories</h3>
        <p>Discover our most loved products and find the perfect additions to your home:</p>
        <ul>
          <li>Elegant Living Room Furniture</li>
          <li>Comfortable Bedroom Collections</li>
          <li>Stylish Dining Sets</li>
          <li>Custom Interior Projects</li>
        </ul>
      </div>

      <p>Ready to start exploring? Click the button below to browse our collections:</p>

      <div style="text-align: center;">
        <a href="{{siteUrl}}/shop" class="button">Explore Our Products</a>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to contact our customer service team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>We look forward to helping you create a beautiful home!</p>

      <p>Warm regards,<br>The Badhees Team</p>
    </div>

    <div class="footer">
      <p>&copy; {{currentYear}} The Badhees. All rights reserved.</p>
      <p>This email was sent to {{customerEmail}}.</p>
    </div>
  </div>
</body>
</html>
`;

// Order confirmation email template
const orderConfirmationTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Confirmation</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eaeaea;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 20px 0;
    }
    .order-details {
      margin: 20px 0;
      border: 1px solid #eaeaea;
      border-radius: 5px;
      padding: 15px;
    }
    .order-items {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
    }
    .order-items th, .order-items td {
      border: 1px solid #eaeaea;
      padding: 10px;
      text-align: left;
    }
    .order-items th {
      background-color: #f5f5f5;
    }
    .footer {
      text-align: center;
      padding: 20px 0;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #eaeaea;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #4a5568;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px 0;
    }
    @media only screen and (max-width: 600px) {
      .container {
        width: 100%;
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="{{logoUrl}}" alt="The Badhees" class="logo">
      <h1>Order Confirmation</h1>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.</p>

      <div class="order-details">
        <h3>Order Details</h3>
        <p><strong>Order Number:</strong> {{orderNumber}}</p>
        <p><strong>Order Date:</strong> {{orderDate}}</p>
        <p><strong>Payment Method:</strong> {{paymentMethod}}</p>
        
        <h4>Items Ordered</h4>
        <table class="order-items">
          <thead>
            <tr>
              <th>Product</th>
              <th>Quantity</th>
              <th>Price</th>
            </tr>
          </thead>
          <tbody>
            {{#each items}}
            <tr>
              <td>{{name}}</td>
              <td>{{quantity}}</td>
              <td>₹{{price}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
        
        <p><strong>Subtotal:</strong> ₹{{subtotal}}</p>
        <p><strong>Shipping:</strong> ₹{{shipping}}</p>
        {{#if discount}}
        <p><strong>Discount:</strong> ₹{{discount}}</p>
        {{/if}}
        <p><strong>Total:</strong> ₹{{total}}</p>
      </div>

      <div class="shipping-address">
        <h3>Shipping Address</h3>
        <p>
          {{shippingAddress.name}}<br>
          {{shippingAddress.address_line1}}<br>
          {{#if shippingAddress.address_line2}}
          {{shippingAddress.address_line2}}<br>
          {{/if}}
          {{shippingAddress.city}}, {{shippingAddress.state}} {{shippingAddress.postal_code}}<br>
          {{shippingAddress.country}}
        </p>
      </div>

      <p>You can track your order status by clicking the button below:</p>

      <div style="text-align: center;">
        <a href="{{trackingUrl}}" class="button">Track Your Order</a>
      </div>

      <p>If you have any questions or concerns about your order, please contact our customer service team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>Thank you for shopping with us!</p>

      <p>Warm regards,<br>The Badhees Team</p>
    </div>

    <div class="footer">
      <p>&copy; {{currentYear}} The Badhees. All rights reserved.</p>
      <p>This email was sent to {{customerEmail}}.</p>
    </div>
  </div>
</body>
</html>
`;

// Delivery confirmation email template
const deliveryConfirmationTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Delivered</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eaeaea;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 20px 0;
    }
    .order-details {
      margin: 20px 0;
      border: 1px solid #eaeaea;
      border-radius: 5px;
      padding: 15px;
    }
    .footer {
      text-align: center;
      padding: 20px 0;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #eaeaea;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #4a5568;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px 0;
    }
    @media only screen and (max-width: 600px) {
      .container {
        width: 100%;
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="{{logoUrl}}" alt="The Badhees" class="logo">
      <h1>Your Order Has Been Delivered!</h1>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Great news! Your order has been delivered.</p>

      <div class="order-details">
        <h3>Order Details</h3>
        <p><strong>Order Number:</strong> {{orderNumber}}</p>
        <p><strong>Order Date:</strong> {{orderDate}}</p>
        <p><strong>Delivery Date:</strong> {{deliveryDate}}</p>
      </div>

      <p>We hope you're enjoying your new items! If you have a moment, we'd love to hear your feedback.</p>

      <div style="text-align: center;">
        <a href="{{reviewUrl}}" class="button">Write a Review</a>
      </div>

      <p>If you have any questions or need assistance with your purchase, please don't hesitate to contact our customer service team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>Thank you for choosing The Badhees!</p>

      <p>Warm regards,<br>The Badhees Team</p>
    </div>

    <div class="footer">
      <p>&copy; {{currentYear}} The Badhees. All rights reserved.</p>
      <p>This email was sent to {{customerEmail}}.</p>
    </div>
  </div>
</body>
</html>
`;

serve(async (req) => {
  // Initialize variables outside try block for cleanup in finally
  let client = null;

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '')

    // Create a Supabase client with the token
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const supabaseClient = createClient(
      supabaseUrl,
      supabaseAnonKey,
      { global: { headers: { Authorization: `Bearer ${token}` } } }
    )

    // Parse the request body
    const { type, userId, orderId } = await req.json()

    // Validate the request
    if (!type) {
      return new Response(JSON.stringify({ error: 'Missing required field: type' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Check if SMTP credentials are available
    if (!EMAIL_USERNAME || !EMAIL_PASSWORD) {
      console.error('Missing SMTP credentials');
      return new Response(JSON.stringify({ error: 'Email service not configured' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Initialize SMTP client
    client = new SmtpClient();

    // Connect to the SMTP server
    try {
      await client.connectTLS({
        hostname: EMAIL_HOST,
        port: EMAIL_PORT,
        username: EMAIL_USERNAME,
        password: EMAIL_PASSWORD,
      });
    } catch (smtpError) {
      console.error('SMTP connection error:', smtpError);
      return new Response(JSON.stringify({ error: 'Failed to connect to email server', details: smtpError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    let emailHtml = '';
    let emailSubject = '';
    let emailSent = false;
    let recipientEmail = '';
    let recipientName = '';
    let emailType = '';

    // Handle different email types
    if (type === 'welcome_email') {
      // Process welcome email
      if (!userId) {
        return new Response(JSON.stringify({ error: 'Missing required field: userId' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Get user details
      const { data: userData, error: userDataError } = await supabaseClient
        .from('user_profiles')
        .select('id, email, display_name')
        .eq('id', userId)
        .single();

      if (userDataError || !userData) {
        return new Response(JSON.stringify({ error: 'User not found', details: userDataError }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      recipientEmail = userData.email;
      recipientName = userData.display_name || 'Valued Customer';
      emailType = 'welcome_email';

      // Check if we've already sent a welcome email to this user
      const { data: emailLog } = await supabaseClient
        .from('email_logs')
        .select('id')
        .eq('email_type', 'welcome_email')
        .eq('recipient', recipientEmail)
        .eq('status', 'sent')
        .maybeSingle();

      if (emailLog) {
        console.log(`Welcome email already sent to ${recipientEmail}`);
        return new Response(JSON.stringify({
          success: true,
          message: 'Welcome email already sent to this user'
        }), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Prepare welcome email
      const currentYear = new Date().getFullYear();
      emailHtml = welcomeEmailTemplate
        .replace(/{{customerName}}/g, recipientName)
        .replace(/{{customerEmail}}/g, recipientEmail)
        .replace(/{{logoUrl}}/g, LOGO_URL)
        .replace(/{{siteUrl}}/g, SITE_URL)
        .replace(/{{currentYear}}/g, currentYear.toString());

      emailSubject = `Welcome to The Badhees - Your Journey to Beautiful Furniture Begins!`;
    } 
    else if (type === 'order_confirmation' || type === 'delivery_confirmation') {
      // Process order emails
      if (!orderId) {
        return new Response(JSON.stringify({ error: 'Missing required field: orderId' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Fetch the order details
      const { data: order, error: orderError } = await supabaseClient
        .from('orders')
        .select(`
          *,
          order_items:order_items(
            *,
            product:products(name, price)
          ),
          user:user_profiles(*)
        `)
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        return new Response(JSON.stringify({ error: 'Order not found', details: orderError }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Fetch the shipping address
      const { data: shippingAddress, error: addressError } = await supabaseClient
        .from('addresses')
        .select('*')
        .eq('id', order.shipping_address_id)
        .single();

      if (addressError) {
        console.error('Error fetching shipping address:', addressError);
      }

      recipientEmail = order.user.email;
      recipientName = order.user.display_name || 'Valued Customer';
      emailType = type;

      // Format order items for the email
      const items = order.order_items.map((item) => ({
        name: item.product.name,
        quantity: item.quantity,
        price: (item.price * item.quantity).toFixed(2)
      }));

      // Calculate totals
      const subtotal = order.subtotal.toFixed(2);
      const shipping = order.shipping_cost.toFixed(2);
      const discount = order.discount ? order.discount.toFixed(2) : 0;
      const total = order.total.toFixed(2);

      // Format dates
      const orderDate = new Date(order.created_at).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const deliveryDate = new Date().toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Get current year for copyright
      const currentYear = new Date().getFullYear();

      // Prepare common template data
      const templateData = {
        customerName: recipientName,
        customerEmail: recipientEmail,
        orderNumber: order.id,
        orderDate,
        items,
        subtotal,
        shipping,
        discount,
        total,
        shippingAddress: shippingAddress || {},
        trackingUrl: `${SITE_URL}/orders/${order.id}`,
        reviewUrl: `${SITE_URL}/orders/${order.id}?review=true`,
        logoUrl: LOGO_URL,
        currentYear
      };

      if (type === 'order_confirmation') {
        // Only send if the order status is 'pending' or 'processing'
        if (order.status !== 'pending' && order.status !== 'processing') {
          return new Response(JSON.stringify({ error: 'Order status not eligible for confirmation email' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          });
        }

        emailSubject = `Order Confirmation #${order.id.substring(0, 8)} - The Badhees`;
        
        // Replace template variables
        emailHtml = orderConfirmationTemplate
          .replace(/{{customerName}}/g, templateData.customerName)
          .replace(/{{customerEmail}}/g, templateData.customerEmail)
          .replace(/{{orderNumber}}/g, templateData.orderNumber)
          .replace(/{{orderDate}}/g, templateData.orderDate)
          .replace(/{{paymentMethod}}/g, order.payment_method || 'Online Payment')
          .replace(/{{subtotal}}/g, templateData.subtotal)
          .replace(/{{shipping}}/g, templateData.shipping)
          .replace(/{{discount}}/g, templateData.discount)
          .replace(/{{total}}/g, templateData.total)
          .replace(/{{trackingUrl}}/g, templateData.trackingUrl)
          .replace(/{{logoUrl}}/g, templateData.logoUrl)
          .replace(/{{currentYear}}/g, templateData.currentYear.toString());
        
        // Replace shipping address
        if (shippingAddress) {
          emailHtml = emailHtml
            .replace(/{{shippingAddress.name}}/g, shippingAddress.name || '')
            .replace(/{{shippingAddress.address_line1}}/g, shippingAddress.address_line1 || '')
            .replace(/{{shippingAddress.address_line2}}/g, shippingAddress.address_line2 || '')
            .replace(/{{shippingAddress.city}}/g, shippingAddress.city || '')
            .replace(/{{shippingAddress.state}}/g, shippingAddress.state || '')
            .replace(/{{shippingAddress.postal_code}}/g, shippingAddress.postal_code || '')
            .replace(/{{shippingAddress.country}}/g, shippingAddress.country || '');
        }
        
        // Replace items (this is a simplified approach)
        let itemsHtml = '';
        items.forEach(item => {
          itemsHtml += `<tr><td>${item.name}</td><td>${item.quantity}</td><td>₹${item.price}</td></tr>`;
        });
        emailHtml = emailHtml.replace(/{{#each items}}[\s\S]*?{{\/each}}/g, itemsHtml);
        
        // Handle conditional discount display
        if (parseFloat(discount) > 0) {
          emailHtml = emailHtml.replace(/{{#if discount}}[\s\S]*?{{\/if}}/g, `<p><strong>Discount:</strong> ₹${discount}</p>`);
        } else {
          emailHtml = emailHtml.replace(/{{#if discount}}[\s\S]*?{{\/if}}/g, '');
        }
      } 
      else if (type === 'delivery_confirmation') {
        // Only send if the order status is 'delivered'
        if (order.status !== 'delivered') {
          return new Response(JSON.stringify({ error: 'Order status not eligible for delivery email' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          });
        }

        emailSubject = `Your Order Has Been Delivered! #${order.id.substring(0, 8)} - The Badhees`;
        
        // Replace template variables
        emailHtml = deliveryConfirmationTemplate
          .replace(/{{customerName}}/g, templateData.customerName)
          .replace(/{{customerEmail}}/g, templateData.customerEmail)
          .replace(/{{orderNumber}}/g, templateData.orderNumber)
          .replace(/{{orderDate}}/g, templateData.orderDate)
          .replace(/{{deliveryDate}}/g, deliveryDate)
          .replace(/{{reviewUrl}}/g, templateData.reviewUrl)
          .replace(/{{logoUrl}}/g, templateData.logoUrl)
          .replace(/{{currentYear}}/g, templateData.currentYear.toString());
      }
    } 
    else {
      return new Response(JSON.stringify({ error: 'Invalid email type' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Send the email
    try {
      await client.send({
        from: EMAIL_FROM,
        to: recipientEmail,
        subject: emailSubject,
        html: emailHtml,
      });

      emailSent = true;
      console.log(`Email sent successfully to ${recipientEmail}`);
    } catch (sendError) {
      console.error('Error sending email:', sendError);
      return new Response(JSON.stringify({ error: 'Failed to send email', details: sendError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Record the email in the database
    if (emailSent) {
      try {
        const { error: logError } = await supabaseClient
          .from('email_logs')
          .insert({
            order_id: type === 'welcome_email' ? null : orderId,
            email_type: emailType,
            recipient: recipientEmail,
            status: 'sent',
            sent_at: new Date().toISOString()
          });

        if (logError) {
          console.error('Error logging email:', logError);
          // Continue even if logging fails
        }
      } catch (logError) {
        console.error('Exception logging email:', logError);
        // Continue even if logging fails
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Email sent successfully'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error in email function:', error);
    return new Response(JSON.stringify({ error: error.message || 'Unknown error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } finally {
    // Always close the SMTP connection if it was opened
    if (client) {
      try {
        await client.close();
      } catch (closeError) {
        console.error('Error closing SMTP connection:', closeError);
      }
    }
  }
})
