# Supabase Configuration
# Replace these with your actual Supabase project values
VITE_SUPABASE_URL=https://tfvbwveohcbghqmxnpbd.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdmJ3dmVvaGNiZ2hxbXhucGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNjQ5NjcsImV4cCI6MjA1OTg0MDk2N30._CnHUHBBWl-d9ldSqNTob0lkgoDjMh6F06abx1KgQlA

# Razorpay Configuration
# Replace these with your actual Razorpay credentials
VITE_RAZORPAY_KEY_ID=rzp_test_hmkupCaieqV4AN
RAZORPAY_SECRET=S7oynWx8EBh3s0sCqyLQLHkv
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Server Port
VITE_SERVER_PORT=3001

# Development Settings
# Uncomment the line below to enable debug mode
# VITE_DEBUG_MODE=true

# Email Configuration
# Add your preferred email service configuration here
# Examples:
# EMAIL_SERVICE=sendgrid
# EMAIL_API_KEY=your_api_key
# EMAIL_FROM=<EMAIL>
