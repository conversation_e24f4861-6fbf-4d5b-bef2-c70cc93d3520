@echo off
echo Testing email functions...

REM Get user ID and order ID
set /p USER_ID="Enter a user ID to test welcome email: "
set /p ORDER_ID="Enter an order ID to test order emails: "

REM Test welcome email
echo Testing welcome email function...
curl -X POST http://localhost:54321/functions/v1/welcome-email ^
  -H "Content-Type: application/json" ^
  -d "{\"userId\":\"%USER_ID%\"}"

echo.
echo.

REM Test order confirmation email
echo Testing order confirmation email function...
curl -X POST http://localhost:54321/functions/v1/order-emails ^
  -H "Content-Type: application/json" ^
  -d "{\"type\":\"order_confirmation\",\"orderId\":\"%ORDER_ID%\"}"

echo.
echo.

REM Test delivery confirmation email
echo Testing delivery confirmation email function...
curl -X POST http://localhost:54321/functions/v1/order-emails ^
  -H "Content-Type: application/json" ^
  -d "{\"type\":\"delivery_confirmation\",\"orderId\":\"%ORDER_ID%\"}"

echo.
echo Email function tests completed!
