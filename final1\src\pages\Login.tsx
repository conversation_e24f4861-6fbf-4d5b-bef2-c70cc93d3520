
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import GoogleLoginButton from '@/components/auth/GoogleLoginButton';
import '@/styles/auth-forms.css';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const { login, isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const redirectParam = searchParams.get('redirect') || '/';
  const redirectPath = redirectParam.startsWith('/') ? redirectParam : `/${redirectParam}`;

  useEffect(() => {
    // If already authenticated, redirect to appropriate dashboard
    if (isAuthenticated) {
      if (redirectPath !== '/') {
        navigate(redirectPath);
      } else if (isAdmin()) {
        navigate('/admin');
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, isAdmin, navigate, redirectPath]);

  const validateForm = (): boolean => {
    let isValid = true;
    setEmailError(null);
    setPasswordError(null);
    setError(null);

    // Email validation
    if (!email) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    // Password validation
    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await login(email, password);
      if (success) {
        // Redirect based on role
        if (email.includes('admin')) {
          navigate('/admin');
        } else {
          navigate(redirectPath);
        }
      } else {
        setError('Invalid email or password. Please try again.');
      }
    } catch (err) {
      setError('An error occurred during login. Please try again.');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-28 pb-16">
        <div className="max-w-md mx-auto px-4 sm:px-8">
          <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-8">
            <h1 className="text-2xl font-bold text-badhees-800 mb-6 text-center">Welcome Back</h1>

            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-badhees-700 mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (emailError) setEmailError(null);
                    if (error) setError(null);
                  }}
                  placeholder="<EMAIL>"
                  aria-describedby={emailError ? "email-error" : undefined}
                  className={`w-full px-4 py-3 rounded-md border ${emailError ? 'border-red-300 focus:ring-red-500' : 'border-badhees-200 focus:ring-badhees-accent'} focus:outline-none focus:ring-1`}
                />
                {emailError && (
                  <p id="email-error" className="mt-1 text-sm text-red-600">{emailError}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-badhees-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (passwordError) setPasswordError(null);
                      if (error) setError(null);
                    }}
                    placeholder="Your password"
                    aria-describedby={passwordError ? "password-error" : undefined}
                    className={`w-full px-4 py-3 rounded-md border ${passwordError ? 'border-red-300 focus:ring-red-500' : 'border-badhees-200 focus:ring-badhees-accent'} focus:outline-none focus:ring-1`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-badhees-500"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {passwordError && (
                  <p id="password-error" className="mt-1 text-sm text-red-600">{passwordError}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="auth-checkbox">
                  <input
                    id="remember-me"
                    type="checkbox"
                  />
                  <label htmlFor="remember-me">
                    Remember me
                  </label>
                </div>

                <Link to="/forgot-password" className="text-sm font-medium text-badhees-accent hover:text-badhees-700">
                  Forgot password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors flex items-center justify-center"
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </button>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>

              <GoogleLoginButton fullWidth />
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-badhees-600">
                Don't have an account?{' '}
                <Link to="/register" className="font-medium text-badhees-accent hover:text-badhees-700">
                  Sign up
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Login;
