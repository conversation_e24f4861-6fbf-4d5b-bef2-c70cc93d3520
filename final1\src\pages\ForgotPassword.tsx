import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { AlertCircle, ArrowLeft, CheckCircle } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const validateEmail = (email: string): boolean => {
    if (!email || !email.trim()) {
      setEmailError('Email is required');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      return;
    }

    setIsLoading(true);
    setEmailError(null);

    try {
      // Get the current URL to construct the redirect URL
      const baseUrl = window.location.origin;
      const redirectUrl = `${baseUrl}/reset-password`;

      console.log('Sending password reset email to:', email);
      console.log('Redirect URL:', redirectUrl);

      // Send password reset email
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      // Show success message
      setIsSuccess(true);
      console.log('Password reset email sent successfully');
    } catch (error: Error | unknown) {
      console.error('Password reset error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred. Please try again.';
      setEmailError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-badhees-50">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
          <div>
            <Link to="/login" className="inline-flex items-center text-sm text-badhees-600 hover:text-badhees-800 mb-6">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to login
            </Link>

            <h2 className="mt-2 text-center text-3xl font-extrabold text-badhees-800">
              Reset your password
            </h2>
            <p className="mt-2 text-center text-sm text-badhees-600">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </div>

          {isSuccess ? (
            <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
              <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-green-800">Password reset email sent</h3>
                <p className="mt-2 text-sm text-green-700">
                  Check your email for a link to reset your password. If it doesn't appear within a few minutes, check your spam folder.
                </p>
                <div className="mt-4">
                  <Link
                    to="/login"
                    className="text-sm font-medium text-green-700 hover:text-green-600"
                  >
                    Return to login
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              {emailError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-700">{emailError}</p>
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-badhees-700 mb-1">
                  Email address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (emailError) setEmailError(null);
                  }}
                  placeholder="<EMAIL>"
                  required
                  className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                />
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-badhees-accent hover:bg-badhees-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-badhees-accent disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Sending...' : 'Send reset link'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ForgotPassword;
