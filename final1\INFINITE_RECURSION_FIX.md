# Fixing Infinite Recursion in Supabase RLS Policies

## The Problem

The application is experiencing critical errors with the message:

```
"infinite recursion detected in policy for relation 'user_profiles'"
```

This error is affecting multiple parts of the application:
- Profile updates fail
- Product fetching fails
- API calls to user_profiles and products endpoints return 500 errors

## Root Cause

The issue is caused by poorly designed Row Level Security (RLS) policies in Supabase that create circular references. Specifically:

1. The policies for `user_profiles` table are checking conditions that themselves trigger the same policy check
2. Functions used in RLS policies (like `is_admin`) are querying the same tables they're protecting
3. This creates an infinite loop (recursion) when the database tries to evaluate the policies

## The Solution

We've implemented a comprehensive fix that addresses the root cause:

1. **Replace recursive policies with non-recursive ones**
2. **Create safe functions that don't cause recursion**
3. **Update application code to use these safe functions**

## How to Apply the Fix

### Step 1: Run the SQL Migration

1. Go to the Supabase Dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of the `src/db/migrations/fix_infinite_recursion.sql` file
4. Paste it into the SQL Editor and run it

This script will:
- Drop all existing problematic policies on the `user_profiles` table
- Create a new `is_admin_safe` function that doesn't cause recursion
- Create simple, non-recursive policies for the `user_profiles` table
- Create safe versions of stored procedures that avoid recursion

### Step 2: Verify the Code Changes

The following files have been updated to use the new safe functions:

1. `src/services/directDbService.ts` - Updated to use the safe versions of the stored procedures:
   - Changed `update_user_profile` to `update_user_profile_safe`
   - Changed `ensure_user_profile` to `ensure_user_profile_safe`

### Step 3: Test the System

After applying the fixes, test the following functionality:

1. **Product Fetching**:
   - Go to the home page and verify products are loading
   - Check the product detail pages
   - Verify category pages load products correctly

2. **Profile Updates**:
   - Log in with a user account
   - Go to the profile page
   - Update the profile information
   - Click "Save Changes"
   - Verify that the changes are saved without errors

## Technical Details

### What Causes Infinite Recursion in RLS Policies?

Infinite recursion in RLS policies typically happens when:

1. A policy for table A references table B
2. Table B has a policy that references table A
3. Or a policy for table A uses a function that queries table A

For example:

```sql
-- This can cause recursion if get_user_role queries user_profiles
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  USING (auth.uid() = id OR get_user_role(auth.uid()) = 'admin');
```

### Our Fix Approach

1. **Simplify policies**: We've created simpler policies that don't reference other tables or use complex functions

2. **Safe admin check**: Created a new `is_admin_safe` function that checks admin status directly from `auth.users` metadata instead of querying `user_profiles`

3. **SECURITY DEFINER functions**: Created stored procedures with SECURITY DEFINER that bypass RLS entirely for critical operations

### Key SQL Changes

1. **Dropped problematic policies**:
```sql
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
-- (and other policies)
```

2. **Created safe admin check**:
```sql
CREATE OR REPLACE FUNCTION is_admin_safe(user_id uuid)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM auth.users 
    WHERE id = user_id 
    AND (
      raw_user_meta_data->>'role' = 'admin' OR 
      raw_app_meta_data->>'role' = 'admin'
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

3. **Created simple policies**:
```sql
CREATE POLICY "Public read access"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (true);
```

## Troubleshooting

If you still encounter issues:

1. **Check Console Errors**: Look for specific error messages in the browser console
2. **Verify SQL Execution**: Make sure the SQL migration script ran successfully
3. **Check RLS Policies**: Run this SQL to verify the policies:
   ```sql
   SELECT * FROM pg_policies WHERE tablename = 'user_profiles';
   ```
4. **Verify Functions**: Check if the safe functions exist:
   ```sql
   SELECT proname FROM pg_proc WHERE proname LIKE '%safe%';
   ```

### Common Error Messages and Solutions

1. **"Function update_user_profile_safe does not exist"**:
   - Run the SQL migration script again to create the function

2. **"Permission denied for table user_profiles"**:
   - Check that the RLS policies are correctly set up
   - Verify that the user is authenticated

## Need Help?

If you continue to experience issues, please contact the development team with:
- The specific error messages from the browser console
- The user ID that's experiencing the issue
- Screenshots of the error
- The time when the error occurred
