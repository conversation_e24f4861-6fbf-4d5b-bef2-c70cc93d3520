@echo off
echo Deploying email functions to production...

REM Check if .env.local exists
if not exist "supabase\.env.local" (
    echo Error: supabase\.env.local file not found!
    echo Please create this file with your email configuration.
    exit /b 1
)

REM Set environment variables from .env.local
echo Setting environment variables in production...
npx supabase secrets set --env-file supabase\.env.local

REM Deploy the functions
echo Deploying functions...
cd supabase\functions
echo Deploying welcome-email function...
npx supabase functions deploy welcome-email
echo Deploying order-emails function...
npx supabase functions deploy order-emails

echo Email functions have been deployed to production!
echo You can check the logs with:
echo npx supabase functions logs welcome-email
echo npx supabase functions logs order-emails
