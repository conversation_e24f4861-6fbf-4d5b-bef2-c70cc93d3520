
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  ShoppingBag,
  Users,
  Package,
  LogOut,
  PlusCircle,
  ChevronDown,
  ChevronRight,
  PackageOpen,
  Scissors,
  UserCog,
  MessageSquare,
  Calendar,
  Database,
  ClipboardList,
  Clock,
  DollarSign,
  UserPlus,
  Star,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/SupabaseAuthContext';

const AdminSidebar = () => {
  const location = useLocation();
  const { logout } = useAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>(['/admin/products', '/admin/completed-projects', '/admin/employees', '/admin/tools']);

  const menuItems = [
    {
      name: 'Dashboard',
      path: '/admin',
      icon: LayoutDashboard
    },
    {
      name: 'Products',
      path: '/admin/products',
      icon: ShoppingBag,
      subItems: [
        { name: 'All Products', path: '/admin/products', icon: ShoppingBag },
        { name: 'Add Product', path: '/admin/products/new', icon: PlusCircle },
      ]
    },
    {
      name: 'Completed Projects',
      path: '/admin/completed-projects',
      icon: PackageOpen,
      subItems: [
        { name: 'All Projects', path: '/admin/completed-projects', icon: PackageOpen },
        { name: 'Add Project', path: '/admin/completed-projects/new', icon: PlusCircle },
      ]
    },
    {
      name: 'Orders',
      path: '/admin/orders',
      icon: Package
    },
    {
      name: 'Customers',
      path: '/admin/customers',
      icon: Users
    },
    {
      name: 'Employee Management',
      path: '/admin/employees',
      icon: UserCog,
      subItems: [
        { name: 'Dashboard', path: '/admin/employees/dashboard', icon: LayoutDashboard },
        { name: 'Employees', path: '/admin/employees', icon: Users },
        { name: 'Add Employee', path: '/admin/employees/new', icon: UserPlus },
        { name: 'Attendance', path: '/admin/employees/attendance', icon: ClipboardList },
        { name: 'Leave Management', path: '/admin/employees/leave', icon: Calendar },
        { name: 'Overtime', path: '/admin/employees/overtime', icon: Clock },
        { name: 'Payroll', path: '/admin/employees/payroll', icon: DollarSign },
      ]
    },
    {
      name: 'Customization Requests',
      path: '/admin/customization-requests',
      icon: Scissors
    },
    {
      name: 'Contact Messages',
      path: '/admin/contact-submissions',
      icon: MessageSquare
    },
    {
      name: 'Consultation Requests',
      path: '/admin/consultation-requests',
      icon: Calendar
    },
    {
      name: 'User Management',
      path: '/admin/users',
      icon: UserCog
    },
    {
      name: 'System Tools',
      path: '/admin/tools',
      icon: Settings,
      subItems: [
        { name: 'Fix Product Ratings', path: '/admin/fix-ratings', icon: Star }
      ]
    }
  ];

  const isActive = (path: string) => {
    return location.pathname === path ||
           (path !== '/admin' && location.pathname.startsWith(path));
  };

  const toggleExpanded = (path: string) => {
    if (expandedItems.includes(path)) {
      setExpandedItems(expandedItems.filter(item => item !== path));
    } else {
      setExpandedItems([...expandedItems, path]);
    }
  };

  const isExpanded = (path: string) => {
    return expandedItems.includes(path) || isActive(path);
  };

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto hidden md:block shadow-sm">
      <div className="p-6 border-b">
        <h2 className="text-2xl font-bold text-badhees-800">Admin Panel</h2>
        <p className="text-sm text-gray-500 mt-1">Manage your store</p>
      </div>

      <nav className="px-3 py-4">
        <ul className="space-y-0.5">
          {menuItems.map(item => (
            <li key={item.name} className="py-0.5">
              {item.subItems ? (
                <div>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between py-2.5 px-3 text-sm font-medium rounded-md",
                      isActive(item.path) && "bg-badhees-50 text-badhees-800"
                    )}
                    onClick={() => toggleExpanded(item.path)}
                  >
                    <div className="flex items-center">
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.name}
                    </div>
                    {isExpanded(item.path) ?
                      <ChevronDown className="h-4 w-4" /> :
                      <ChevronRight className="h-4 w-4" />
                    }
                  </Button>

                  {/* Sub-items */}
                  {isExpanded(item.path) && (
                    <ul className="mt-1 ml-6 space-y-0.5">
                      {item.subItems.map(subItem => (
                        <li key={subItem.name}>
                          <Link to={subItem.path}>
                            <Button
                              variant="ghost"
                              className={cn(
                                "w-full justify-start py-2 text-sm font-medium rounded-md",
                                location.pathname === subItem.path && "bg-badhees-50 text-badhees-800"
                              )}
                            >
                              <subItem.icon className="mr-2 h-3.5 w-3.5" />
                              {subItem.name}
                            </Button>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link to={item.path}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start py-2.5 px-3 text-sm font-medium rounded-md",
                      isActive(item.path) && "bg-badhees-50 text-badhees-800"
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.name}
                  </Button>
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <div className="px-3 py-4 mt-auto border-t">
        <Button
          variant="outline"
          className="w-full justify-start text-sm font-medium text-gray-700"
          onClick={logout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );
};

export default AdminSidebar;
