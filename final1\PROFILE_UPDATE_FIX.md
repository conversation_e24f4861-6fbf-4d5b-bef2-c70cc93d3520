# Profile Update Fix

This guide explains how to fix the profile update issue in the application.

## The Problem

Users are experiencing errors when trying to update their profiles:
- "Error updating profile: There was a problem updating your profile. Please try again."
- Console errors showing "Error updating user profile" and "Failed to update profile in Supabase"

The issue is related to Row Level Security (RLS) policies in Supabase that are preventing users from updating their profiles.

## The Solution

We've implemented a comprehensive fix that addresses these issues at multiple levels:

1. **Database Level**: Created stored procedures with SECURITY DEFINER to bypass RLS
2. **Service Level**: Added multiple fallback mechanisms for profile updates
3. **UI Level**: Improved error handling and logging

## How to Apply the Fix

### Step 1: Run the SQL Migration

1. Go to the Supabase Dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of the `src/db/migrations/fix_profile_update_permissions.sql` file
4. Paste it into the SQL Editor and run it

This script will:
- Fix the RLS policies to ensure users can access their profiles
- Create stored procedures with SECURITY DEFINER to bypass RLS
- Create a function to ensure user profiles exist

### Step 2: Verify the Code Changes

Make sure the following files have been updated:

1. `src/services/userProfileService.ts` - Added robust error handling and fallback mechanisms
2. `src/services/directDbService.ts` - Added direct database access functions
3. `src/pages/Profile.tsx` - Updated to use the direct database service

### Step 3: Test the System

After applying the fixes, test the profile update functionality:

1. Log in with a user account
2. Go to the profile page
3. Update the profile information
4. Click "Save Changes"
5. Verify that the changes are saved without errors

## Technical Details

### Database Changes

1. **Fixed RLS Policies**:
   - Simplified policies to avoid recursion
   - Added policies for users to view and update their own profiles

2. **Created Stored Procedures**:
   - `update_user_profile`: A function with SECURITY DEFINER to bypass RLS
   - `ensure_user_profile`: A function to create profiles if they don't exist

### Code Changes

1. **Direct Database Service**:
   - Created a new service for direct database access
   - Added functions to call the stored procedures

2. **User Profile Service**:
   - Enhanced error handling in `updateUserProfile`
   - Added multiple fallback mechanisms (RPC, update, upsert, insert)

3. **Profile Page**:
   - Updated to use the direct database service
   - Added fallback to regular update if direct update fails

## Troubleshooting

If you still encounter issues:

1. **Check Console Errors**: Look for specific error messages in the browser console
2. **Verify Database Structure**: Make sure the `user_profiles` table has all required columns
3. **Check RLS Policies**: Ensure the policies allow users to access their profiles
4. **Verify Stored Procedures**: Make sure the stored procedures are correctly created

### Common Error Messages and Solutions

1. **"Function update_user_profile does not exist"**:
   - Run the SQL migration script again to create the function

2. **"Permission denied for table user_profiles"**:
   - Check that the RLS policies are correctly set up
   - Verify that the user is authenticated

3. **"Invalid input syntax for type uuid"**:
   - Make sure the user ID is a valid UUID

## Manual Profile Update

If needed, you can manually update a profile for a user using SQL:

```sql
-- First ensure the profile exists
SELECT ensure_user_profile('user-id-here');

-- Then update the profile
SELECT update_user_profile(
  'user-id-here',
  'User Name',
  '+1234567890',
  NULL,
  '123 Main St',
  'City',
  'State',
  '12345',
  'Country',
  NULL
);
```

## Need Help?

If you continue to experience issues, please contact the development team with:
- The specific error messages from the browser console
- The user ID that's experiencing the issue
- Screenshots of the profile page
- The time when the error occurred
