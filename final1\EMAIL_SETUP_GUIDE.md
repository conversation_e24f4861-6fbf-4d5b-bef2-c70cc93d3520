# Email Functions Setup Guide

This guide provides step-by-step instructions for setting up and running the email functions for The Badhees website.

## Prerequisites

- Node.js installed
- Supabase project set up
- Gmail account with app password (already configured in `.env.local`)

## Quick Start

1. Run the setup script to start the email functions locally:

```
setup_email_functions.bat
```

2. Test the functions using the test script:

```
test_email_functions.bat
```

3. When ready to deploy to production, run:

```
deploy_email_functions.bat
```

## Manual Setup

If you prefer to run the commands manually, follow these steps:

### 1. Set Environment Variables

The `.env.local` file in the `supabase` directory should contain:

```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=psmr krvj ccdv vphq
EMAIL_FROM=<EMAIL>
SITE_URL=http://localhost:5173
```

Set these environment variables in your local Supabase instance:

```bash
npx supabase secrets set --env-file ./supabase/.env.local
```

### 2. Start Edge Functions Locally

```bash
cd supabase/functions
npx supabase functions serve welcome-email --no-verify-jwt
npx supabase functions serve order-emails --no-verify-jwt
```

### 3. Test the Functions

Use curl or Postman to test the functions:

```bash
# Test welcome email
curl -X POST http://localhost:54321/functions/v1/welcome-email \
  -H "Content-Type: application/json" \
  -d '{"userId":"YOUR_USER_ID"}'

# Test order confirmation email
curl -X POST http://localhost:54321/functions/v1/order-emails \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"YOUR_ORDER_ID"}'
```

Replace `YOUR_USER_ID` and `YOUR_ORDER_ID` with actual IDs from your database.

### 4. Deploy to Production

```bash
cd supabase/functions
npx supabase functions deploy welcome-email
npx supabase functions deploy order-emails
```

## Troubleshooting

### Common Issues

1. **"Command not found" errors**
   - Make sure you've installed the Supabase CLI: `npm install -g supabase`
   - Or use npx: `npx supabase`

2. **Emails not sending**
   - Check Edge Function logs: `npx supabase functions logs welcome-email`
   - Verify SMTP credentials are correct
   - Check if your email provider is blocking the connection

3. **"Cannot find module" errors**
   - Make sure you've deployed the functions with the correct dependencies
   - Check import paths in the function code

4. **Authentication errors**
   - In production, make sure you're providing a valid JWT token
   - For local testing, use the `--no-verify-jwt` flag

### Viewing Logs

```bash
# View logs for welcome email function
npx supabase functions logs welcome-email

# View logs for order emails function
npx supabase functions logs order-emails
```

## Email Templates

The email templates are located at:

- `supabase/functions/welcome-email/templates/welcome.html`
- `supabase/functions/order-emails/templates/order-confirmation.html`
- `supabase/functions/order-emails/templates/delivery-confirmation.html`

To customize these templates:

1. Edit the HTML files
2. Re-deploy the functions:
   ```bash
   npx supabase functions deploy welcome-email
   npx supabase functions deploy order-emails
   ```

## Security Best Practices

1. **Never commit email credentials to your repository**
2. **Use environment variables** for all sensitive information
3. **Implement rate limiting** to prevent abuse
4. **Validate email recipients** before sending
5. **Use Row Level Security** to protect email logs
6. **Sanitize all user input** used in email templates
