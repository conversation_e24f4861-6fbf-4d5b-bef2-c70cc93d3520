import React, { useState, useEffect, useRef } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import useEmblaCarousel from 'embla-carousel-react';
import { useVisibilityAwareInterval } from '@/hooks/use-optimized-render';

interface AutoChangingCarouselProps {
  images: string[];
  projectName: string;
}

const AutoChangingCarousel = ({ images, projectName }: AutoChangingCarouselProps) => {
  console.log('AutoChangingCarousel received images:', images);

  // Ensure images is an array
  const validImages = Array.isArray(images) ? images : [];

  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [isUserInteracting, setIsUserInteracting] = useState(false);

  // Auto-scroll with tab visibility awareness
  useVisibilityAwareInterval(
    () => {
      if (emblaApi) {
        emblaApi.scrollNext();
      }
    },
    4000,
    validImages.length > 1 && !isUserInteracting
  );

  // Set up user interaction handlers
  useEffect(() => {
    if (emblaApi) {
      const onPointerDown = () => {
        setIsUserInteracting(true);
      };

      const onPointerUp = () => {
        // Resume auto-scrolling after a delay
        setTimeout(() => {
          setIsUserInteracting(false);
        }, 2000);
      };

      emblaApi.on('pointerDown', onPointerDown);
      emblaApi.on('pointerUp', onPointerUp);

      return () => {
        emblaApi.off('pointerDown', onPointerDown);
        emblaApi.off('pointerUp', onPointerUp);
      };
    }
  }, [emblaApi]);

  // If no valid images, show placeholder
  if (validImages.length === 0) {
    return (
      <div className="aspect-w-16 aspect-h-10 flex items-center justify-center rounded-lg bg-badhees-100">
        <span className="text-badhees-600">No Images Available</span>
      </div>
    );
  }

  // If only one image, show it without carousel
  if (validImages.length === 1) {
    return (
      <div className="aspect-w-16 aspect-h-10 relative overflow-hidden rounded-lg bg-badhees-100">
        <img
          src={validImages[0]}
          alt={`${projectName}`}
          className="w-full h-full object-cover"
        />
      </div>
    );
  }

  // Multiple images, show carousel
  return (
    <Carousel className="w-full" ref={emblaRef}>
      <CarouselContent>
        {validImages.map((image, index) => (
          <CarouselItem key={index} className="w-full">
            <div className="aspect-w-16 aspect-h-10 relative overflow-hidden rounded-lg bg-badhees-100">
              <img
                src={image}
                alt={`${projectName} - Image ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <div className="flex justify-center mt-2">
        <CarouselPrevious className="relative inset-0 translate-y-0 mr-2" />
        <CarouselNext className="relative inset-0 translate-y-0" />
      </div>
    </Carousel>
  );
};

export default AutoChangingCarousel;
