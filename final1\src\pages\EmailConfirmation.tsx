import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';

const EmailConfirmation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const confirmEmail = async () => {
      try {
        // Get the token from URL parameters
        const token = searchParams.get('token');
        const type = searchParams.get('type');

        if (!token) {
          setStatus('error');
          setMessage('Invalid confirmation link. Please check your email and try again.');
          return;
        }

        // Verify the email with Supabase
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: type === 'signup' ? 'signup' : 'email'
        });

        if (error) {
          console.error('Email confirmation error:', error);
          setStatus('error');

          if (error.message.includes('expired')) {
            setMessage('The confirmation link has expired. Please request a new confirmation email.');
          } else if (error.message.includes('already been used')) {
            setMessage('This confirmation link has already been used. If you haven\'t confirmed your email yet, please request a new link.');
          } else {
            setMessage('Failed to confirm your email. The link may have expired or already been used.');
          }
          return;
        }

        if (data.user) {
          setStatus('success');
          setMessage('Your email has been successfully confirmed! You can now sign in to your account.');
        } else {
          setStatus('error');
          setMessage('Email confirmation failed. Please try again or contact support.');
        }
      } catch (error) {
        console.error('Unexpected error during email confirmation:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again later.');
      }
    };

    confirmEmail();
  }, [searchParams]);

  const handleContinue = () => {
    if (status === 'success') {
      navigate('/login', {
        state: {
          message: 'Email confirmed successfully! Please sign in to continue.'
        }
      });
    } else {
      navigate('/register');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="pt-28 pb-16">
        <div className="max-w-md mx-auto px-4 sm:px-8">
          <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-8 text-center">

            {/* Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-badhees-800 mb-2">
                Email Confirmation
              </h1>
              <p className="text-sm text-badhees-600">
                The Badhees - Premium Furniture Store
              </p>
            </div>

            {/* Status Icon and Message */}
            <div className="mb-8">
              {status === 'loading' && (
                <div className="flex flex-col items-center">
                  <Loader2 className="h-16 w-16 text-badhees-600 animate-spin mb-4" />
                  <p className="text-badhees-700 font-medium">
                    Confirming your email...
                  </p>
                  <p className="text-sm text-badhees-500 mt-2">
                    Please wait while we verify your email address.
                  </p>
                </div>
              )}

              {status === 'success' && (
                <div className="flex flex-col items-center">
                  <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
                  <p className="text-green-700 font-medium mb-2">
                    Email Confirmed Successfully!
                  </p>
                  <p className="text-sm text-badhees-600">
                    {message}
                  </p>
                </div>
              )}

              {status === 'error' && (
                <div className="flex flex-col items-center">
                  <XCircle className="h-16 w-16 text-red-500 mb-4" />
                  <p className="text-red-700 font-medium mb-2">
                    Confirmation Failed
                  </p>
                  <p className="text-sm text-badhees-600">
                    {message}
                  </p>
                </div>
              )}
            </div>

            {/* Action Button */}
            {status !== 'loading' && (
              <div className="space-y-4">
                <button
                  type="button"
                  onClick={handleContinue}
                  className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors"
                >
                  {status === 'success' ? 'Continue to Sign In' : 'Try Again'}
                </button>

                {status === 'error' && (
                  <p className="text-xs text-badhees-500">
                    If you continue to experience issues, please contact our support team at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-badhees-600 hover:text-badhees-700 underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                )}
              </div>
            )}

            {/* Company Info */}
            <div className="mt-8 pt-6 border-t border-badhees-100">
              <p className="text-xs text-badhees-500">
                This email confirmation is from The Badhees, your trusted partner for premium furniture.
                <br />
                <a
                  href="https://thebadhees.com"
                  className="text-badhees-600 hover:text-badhees-700 underline"
                >
                  Visit our website
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default EmailConfirmation;
