# Email Function Deployment Guide

This guide provides instructions for deploying the unified email function for The Badhees website.

## Overview

The email function has been simplified into a single Edge Function that handles all email types:

1. Welcome emails
2. Order confirmation emails
3. Delivery confirmation emails

## Prerequisites

- Supabase account with access to your project
- Supabase CLI installed (you can use `npx supabase` to run commands)
- Email configuration (SMTP credentials)

## Deployment Steps

### 1. Login to Supabase

```bash
npx supabase login
```

This will open a browser window where you can authenticate with Supabase.

### 2. Link Your Project

```bash
npx supabase link --project-ref YOUR_PROJECT_ID
```

Replace `YOUR_PROJECT_ID` with your Supabase project ID from the URL of your Supabase dashboard.

### 3. Set Environment Variables

Set the email environment variables in your Supabase project:

```bash
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD="psmr krvj ccdv vphq"
npx supabase secrets set EMAIL_FROM=<EMAIL>
npx supabase secrets set SITE_URL=https://thebadhees.com
```

Note: Replace `https://thebadhees.com` with your actual production URL.

### 4. Deploy the Email Function

```bash
cd supabase/functions
npx supabase functions deploy email-service
```

### 5. Verify Deployment

Check the logs to verify the function was deployed successfully:

```bash
npx supabase functions logs email-service
```

## Testing the Email Function

You can test the function using the Supabase dashboard or by making HTTP requests:

### Welcome Email

```bash
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/email-service \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"USER_ID"}'
```

### Order Confirmation Email

```bash
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/email-service \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"ORDER_ID"}'
```

### Delivery Confirmation Email

```bash
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/email-service \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"type":"delivery_confirmation","orderId":"ORDER_ID"}'
```

Replace:
- `YOUR_PROJECT_ID` with your Supabase project ID
- `YOUR_ANON_KEY` with your Supabase anon key
- `USER_ID` with a valid user ID from your database
- `ORDER_ID` with a valid order ID from your database

## Email Templates

The email templates are located at:

- `supabase/functions/email-service/templates/welcome.html`
- `supabase/functions/email-service/templates/order-confirmation.html`
- `supabase/functions/email-service/templates/delivery-confirmation.html`

To customize these templates:

1. Edit the HTML files
2. Re-deploy the function:
   ```bash
   npx supabase functions deploy email-service
   ```

## Troubleshooting

### Common Issues

1. **Authentication errors**
   - Make sure you're logged in to Supabase: `npx supabase login`
   - Verify your project is linked: `npx supabase link --project-ref YOUR_PROJECT_ID`

2. **Deployment errors**
   - Check the function logs: `npx supabase functions logs email-service`
   - Verify your environment variables are set correctly

3. **Email sending errors**
   - Verify your SMTP credentials are correct
   - Check if your email provider is blocking the connection
   - Gmail limits sending to 500 emails per day

## Frontend Integration

The frontend code has been updated to use the new unified email function. The email service functions now call the `email-service` Edge Function instead of the separate functions.

## Security Considerations

1. **Never commit email credentials to your repository**
2. **Use environment variables** for all sensitive information
3. **Implement rate limiting** to prevent abuse
4. **Validate email recipients** before sending
5. **Use Row Level Security** to protect email logs
6. **Sanitize all user input** used in email templates
