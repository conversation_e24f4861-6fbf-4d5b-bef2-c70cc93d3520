/**
 * Email Function Test Script
 * 
 * This script helps test the email functionality by making direct API calls to the Supabase Edge Function.
 * 
 * Usage:
 * 1. Make sure you have Node.js installed
 * 2. Run: node test-email-function.js <type> <id>
 * 
 * Examples:
 * - Test welcome email: node test-email-function.js welcome_email 1ddf5ad6-54dc-4ef5-b186-314a066ad43c
 * - Test order confirmation: node test-email-function.js order_confirmation <order_id>
 * - Test delivery confirmation: node test-email-function.js delivery_confirmation <order_id>
 */

const fetch = require('node-fetch');

// Configuration
const SUPABASE_URL = 'https://tfvbwveohcbghqmxnpbd.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdmJ3dmVvaGNiZ2hxbXhucGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNjQ5NjcsImV4cCI6MjA1OTg0MDk2N30._CnHUHBBWl-d9ldSqNTob0lkgoDjMh6F06abx1KgQlA';

// Get command line arguments
const args = process.argv.slice(2);
const emailType = args[0];
const id = args[1];

if (!emailType || !id) {
  console.error('Usage: node test-email-function.js <type> <id>');
  console.error('Example: node test-email-function.js welcome_email 1ddf5ad6-54dc-4ef5-b186-314a066ad43c');
  process.exit(1);
}

// Prepare request body based on email type
let requestBody = {};

if (emailType === 'welcome_email') {
  requestBody = {
    type: 'welcome_email',
    userId: id
  };
} else if (emailType === 'order_confirmation' || emailType === 'delivery_confirmation') {
  requestBody = {
    type: emailType,
    orderId: id
  };
} else {
  console.error('Invalid email type. Supported types: welcome_email, order_confirmation, delivery_confirmation');
  process.exit(1);
}

// Make the API call
async function testEmailFunction() {
  try {
    console.log(`Testing ${emailType} with ID: ${id}`);
    console.log('Request body:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/email-service`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify(requestBody)
    });
    
    const data = await response.json();
    
    console.log('Status code:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Email function call successful!');
    } else {
      console.error('❌ Email function call failed!');
    }
  } catch (error) {
    console.error('Error calling email function:', error);
  }
}

testEmailFunction();
