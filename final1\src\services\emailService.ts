/**
 * Email Service
 *
 * This service handles sending emails through Supabase Edge Functions.
 */
import { supabase } from '@/lib/supabase';

/**
 * Email template types
 */
export enum EmailTemplate {
  WELCOME = 'welcome_email',
  ORDER_CONFIRMATION = 'order_confirmation',
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  ORDER_SHIPPED = 'order_shipped',
  ORDER_DELIVERED = 'delivery_confirmation', // Updated to match Edge Function
  ORDER_CANCELLED = 'order_cancelled',
}

/**
 * Email data interface
 */
export interface EmailData {
  to: string;
  subject?: string;
  templateId: EmailTemplate;
  data: Record<string, any>;
}

/**
 * Send an email using Supabase Edge Functions
 *
 * @param emailData Email data including recipient, template, and data
 * @returns Success status
 */
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Insert into the emails table to trigger the database function
    const { error } = await supabase
      .from('email_queue')
      .insert({
        recipient_email: emailData.to,
        template_id: emailData.templateId,
        subject: emailData.subject,
        data: emailData.data,
        status: 'pending'
      });

    if (error) {
      console.error('Error queueing email:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
};

/**
 * Send order confirmation email using the existing email queue system
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @returns Success status
 */
export const sendOrderConfirmationEmail = async (
  orderId: string,
  email: string
): Promise<boolean> => {
  try {
    // Check if we've already sent this email
    const { data: emailLog } = await supabase
      .from('email_logs')
      .select('id')
      .eq('order_id', orderId)
      .eq('email_type', 'order_confirmation')
      .eq('status', 'sent')
      .maybeSingle();

    if (emailLog) {
      console.log(`Order confirmation email for order ${orderId} already sent`);
      return true; // Email already sent
    }

    // Try to use the new Edge Function first
    try {
      const result = await sendOrderConfirmationViaEdgeFunction(orderId);
      if (result) {
        return true;
      }
    } catch (edgeFunctionError) {
      console.warn('Failed to send via Edge Function, falling back to email queue:', edgeFunctionError);
    }

    // Get order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        shipping_address:shipping_addresses(*),
        order_items:order_items(
          *,
          product:products(*)
        )
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order for email:', orderError);
      return false;
    }

    // Format order data for email
    const formattedOrder = {
      id: order.id,
      order_number: order.id.substring(0, 8).toUpperCase(),
      created_at: new Date(order.created_at).toLocaleDateString(),
      total_amount: order.total_amount.toFixed(2),
      payment_method: order.payment_method,
      payment_status: order.payment_status,
      shipping_address: order.shipping_address,
      items: order.order_items.map((item: any) => ({
        name: item.product.name,
        quantity: item.quantity,
        price: item.price.toFixed(2),
        total: (item.price * item.quantity).toFixed(2),
        image: item.product.image
      }))
    };

    // Send email
    return await sendEmail({
      to: email,
      templateId: EmailTemplate.ORDER_CONFIRMATION,
      subject: `Order Confirmation #${formattedOrder.order_number}`,
      data: {
        order: formattedOrder,
        customer_name: order.shipping_address?.full_name || email.split('@')[0]
      }
    });
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    return false;
  }
};

/**
 * Send order confirmation email using the Edge Function
 *
 * @param orderId Order ID
 * @returns Success status
 */
export const sendOrderConfirmationViaEdgeFunction = async (orderId: string): Promise<boolean> => {
  try {
    console.log(`Sending order confirmation email via Edge Function for order ${orderId}`);

    // Check if we've already sent this email
    const { data: emailLog, error: logError } = await supabase
      .from('email_logs')
      .select('id')
      .eq('order_id', orderId)
      .eq('email_type', 'order_confirmation')
      .eq('status', 'sent')
      .maybeSingle();

    if (logError) {
      console.warn('Error checking email logs:', logError);
      // Continue anyway - we'll just risk sending a duplicate email
    } else if (emailLog) {
      console.log(`Order confirmation email for order ${orderId} already sent`);
      return true; // Email already sent
    }

    // Get the user's session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return false;
    }

    if (!session) {
      console.error('No active session found');
      // Try to get a service role key or JWT token for server-side calls
      // This is important for automated emails or admin-triggered emails
      const serviceKey = import.meta.env.VITE_SUPABASE_SERVICE_KEY || '';
      const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

      // Try with service key first, fall back to anon key if needed
      const authKey = serviceKey || anonKey;

      if (!authKey) {
        console.error('No authentication key available');
        return false;
      }

      // Call the Edge Function with the available key
      const { data, error } = await supabase.functions.invoke('email-service', {
        body: {
          type: 'order_confirmation',
          orderId
        },
        headers: {
          Authorization: `Bearer ${authKey}`
        }
      });

      if (error) {
        console.error('Error sending email with service key:', error);
        return false;
      }

      console.log('Order confirmation email sent successfully with service key:', data);
      return true;
    }

    // Call the Edge Function with user session
    const { data, error } = await supabase.functions.invoke('email-service', {
      body: {
        type: 'order_confirmation',
        orderId
      },
      headers: {
        Authorization: `Bearer ${session.access_token}`
      }
    });

    if (error) {
      console.error('Error sending order confirmation email via Edge Function:', error);
      return false;
    }

    console.log('Order confirmation email sent successfully via Edge Function:', data);
    return true;
  } catch (error) {
    console.error('Exception sending order confirmation email via Edge Function:', error);
    return false;
  }
};

/**
 * Send payment success email
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @param paymentId Payment ID
 * @returns Success status
 */
export const sendPaymentSuccessEmail = async (
  orderId: string,
  email: string,
  paymentId: string
): Promise<boolean> => {
  try {
    // Get order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        shipping_address:shipping_addresses(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order for payment email:', orderError);
      return false;
    }

    // Format payment data for email
    const paymentData = {
      order_id: order.id,
      order_number: order.id.substring(0, 8).toUpperCase(),
      payment_id: paymentId,
      amount: order.total_amount.toFixed(2),
      payment_date: new Date().toLocaleDateString(),
      payment_method: order.payment_method || 'Online Payment'
    };

    // Send email
    return await sendEmail({
      to: email,
      templateId: EmailTemplate.PAYMENT_SUCCESS,
      subject: `Payment Successful for Order #${paymentData.order_number}`,
      data: {
        payment: paymentData,
        customer_name: order.shipping_address?.full_name || email.split('@')[0]
      }
    });
  } catch (error) {
    console.error('Error sending payment success email:', error);
    return false;
  }
};

/**
 * Send order status update email
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @param status New order status
 * @returns Success status
 */
/**
 * Send welcome email to a new user
 *
 * @param userId User ID
 * @param email User's email address
 * @returns Success status
 */
export const sendWelcomeEmail = async (
  userId: string,
  email: string
): Promise<boolean> => {
  try {
    console.log(`Sending welcome email to user ${userId} (${email})`);

    // Check if we've already sent this email
    const { data: emailLog, error: logError } = await supabase
      .from('email_logs')
      .select('id')
      .eq('email_type', 'welcome_email')
      .eq('recipient', email)
      .eq('status', 'sent')
      .maybeSingle();

    if (logError) {
      console.warn('Error checking email logs:', logError);
      // Continue anyway - we'll just risk sending a duplicate email
    } else if (emailLog) {
      console.log(`Welcome email for user ${userId} already sent`);
      return true; // Email already sent
    }

    // Get the user's session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return false;
    }

    if (session) {
      // Call the Edge Function with user session
      const { data, error } = await supabase.functions.invoke('email-service', {
        body: {
          type: 'welcome_email',
          userId
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      });

      if (!error) {
        console.log('Welcome email sent successfully:', data);
        return true;
      }

      console.error('Error sending welcome email with session:', error);
    }

    // If no session or session call failed, try with service key or anon key
    const serviceKey = import.meta.env.VITE_SUPABASE_SERVICE_KEY || '';
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

    // Try with service key first, fall back to anon key if needed
    const authKey = serviceKey || anonKey;

    if (authKey) {
      const { data, error } = await supabase.functions.invoke('email-service', {
        body: {
          type: 'welcome_email',
          userId
        },
        headers: {
          Authorization: `Bearer ${authKey}`
        }
      });

      if (error) {
        console.error('Error sending welcome email with authentication key:', error);
        return false;
      }

      console.log('Welcome email sent successfully with authentication key:', data);
      return true;
    }

    // If Edge Function fails or no authentication is available, use the email queue
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile for welcome email:', profileError);
      return false;
    }

    // Send email through queue
    return await sendEmail({
      to: email,
      templateId: EmailTemplate.WELCOME,
      subject: 'Welcome to The Badhees - Your Journey to Beautiful Furniture Begins!',
      data: {
        customer_name: profile.display_name || email.split('@')[0]
      }
    });
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
};

export const sendOrderStatusUpdateEmail = async (
  orderId: string,
  email: string,
  status: string
): Promise<boolean> => {
  try {
    console.log(`Sending order status update email for order ${orderId} with status ${status}`);

    // Check if we've already sent this email
    const emailType = status === 'delivered' ? 'delivery_confirmation' : `order_${status}`;
    const { data: emailLog, error: logError } = await supabase
      .from('email_logs')
      .select('id')
      .eq('order_id', orderId)
      .eq('email_type', emailType)
      .eq('status', 'sent')
      .maybeSingle();

    if (logError) {
      console.warn('Error checking email logs:', logError);
      // Continue anyway - we'll just risk sending a duplicate email
    } else if (emailLog) {
      console.log(`Email for order ${orderId} with status ${status} already sent`);
      return true; // Email already sent
    }

    // Get the user's session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return false;
    }

    // For delivery confirmation, use the new Edge Function
    if (status === 'delivered') {
      // Try with session if available
      if (session) {
        // Call the Edge Function with user session
        const { data, error } = await supabase.functions.invoke('email-service', {
          body: {
            type: 'delivery_confirmation',
            orderId
          },
          headers: {
            Authorization: `Bearer ${session.access_token}`
          }
        });

        if (!error) {
          console.log('Delivery confirmation email sent successfully:', data);
          return true;
        }

        console.error('Error sending delivery confirmation email with session:', error);
      }

      // If no session or session call failed, try with service key or anon key
      const serviceKey = import.meta.env.VITE_SUPABASE_SERVICE_KEY || '';
      const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

      // Try with service key first, fall back to anon key if needed
      const authKey = serviceKey || anonKey;

      if (authKey) {
        const { data, error } = await supabase.functions.invoke('email-service', {
          body: {
            type: 'delivery_confirmation',
            orderId
          },
          headers: {
            Authorization: `Bearer ${authKey}`
          }
        });

        if (error) {
          console.error('Error sending delivery confirmation email with authentication key:', error);
          return false;
        }

        console.log('Delivery confirmation email sent successfully with authentication key:', data);
        return true;
      }

      console.error('No authentication method available for sending email');
      return false;
    }

    // For other statuses, use the existing email queue system
    // Get order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        shipping_address:shipping_addresses(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order for status update email:', orderError);
      return false;
    }

    // Determine which template to use based on status
    let templateId: EmailTemplate;
    let subject: string;

    switch (status) {
      case 'shipped':
        templateId = EmailTemplate.ORDER_SHIPPED;
        subject = `Your Order #${order.id.substring(0, 8).toUpperCase()} Has Been Shipped`;
        break;
      case 'cancelled':
        templateId = EmailTemplate.ORDER_CANCELLED;
        subject = `Your Order #${order.id.substring(0, 8).toUpperCase()} Has Been Cancelled`;
        break;
      default:
        return false; // Unsupported status
    }

    // Send email
    return await sendEmail({
      to: email,
      templateId,
      subject,
      data: {
        order: {
          id: order.id,
          order_number: order.id.substring(0, 8).toUpperCase(),
          status,
          updated_at: new Date().toLocaleDateString(),
          tracking_number: order.tracking_number || 'Not available yet'
        },
        customer_name: order.shipping_address?.full_name || email.split('@')[0]
      }
    });
  } catch (error) {
    console.error('Error sending order status update email:', error);
    return false;
  }
};
